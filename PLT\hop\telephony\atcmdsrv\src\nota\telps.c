/******************************************************************************

*(C) Copyright 2007 Marvell International Ltd.

* All Rights Reserved

******************************************************************************/
/*--------------------------------------------------------------------------------------------------------------------
 *	INTEL CONFIDENTIAL
 *	Copyright 2006 Intel Corporation All Rights Reserved.
 *	The source code contained or described herein and all documents related to the source code (�Material? are owned
 *	by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
 *	its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
 *	Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
 *	treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
 *	transmitted, distributed, or disclosed in any way without Intel�s prior express written permission.
 *
 *	No license under any patent, copyright, trade secret or other intellectual property right is granted to or
 *	conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
 *	estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
 *	Intel in writing.
 *	-------------------------------------------------------------------------------------------------------------------
 *
 *  Filename: telps.c
 *
 *  Authors: <AUTHORS>
 *
 *  Description: Process packet switched service related AT commands
 *
 *  History:
 *   Feb 18, 2008 - Creation of file
 *
 *  Notes:
 *
 ******************************************************************************/

/******************************************************************************
 *   include files
 ******************************************************************************/
#include "ci_api_types.h"
#include "ci_api_client.h"
#include "teldef.h"
#include "telutl.h"
#include "telatparamdef.h"
#include "ci_ps.h"
#include "telatci.h"
#include "ps_api.h"
#include "telps.h"
#include "utlMalloc.h"
#include "duster.h"

#ifdef SUPPORT_CM_DUSTER_DIALER
#include "connect_management.h"
#endif
#ifdef PSM_ENABLE
#include "psm_wrapper.h"
#endif

#include "ursp_inner.h"



#ifndef MIN_SYS

#if defined (UPGRADE_R99)

/* QoS related structure */
static const ErrorRatioLookup errorRatioSDU[] =
{
	{ 0,                                  0,                                  TEL_GPRS_SDU_ERROR_RATIO_SUBSCRIBED   },
	{ 1,                                  2,                                  TEL_GPRS_SDU_ERROR_RATIO_1102         },
	{ 7,                                  3,                                  TEL_GPRS_SDU_ERROR_RATIO_7103         },
	{ 1,                                  3,                                  TEL_GPRS_SDU_ERROR_RATIO_1103         },
	{ 1,                                  4,                                  TEL_GPRS_SDU_ERROR_RATIO_1104         },
	{ 1,                                  5,                                  TEL_GPRS_SDU_ERROR_RATIO_1105         },
	{ 1,                                  6,                                  TEL_GPRS_SDU_ERROR_RATIO_1106         },
	{ 1,                                  1,                                  TEL_GPRS_SDU_ERROR_RATIO_1101         },
	{ CI_PS_3G_ENUM_LOOKUP_END_ELEMENT,   CI_PS_3G_ENUM_LOOKUP_END_ELEMENT,   CI_PS_3G_ENUM_LOOKUP_END_ELEMENT  }
};

static const ErrorRatioLookup errorRatioBER[] =
{
	{ 0,                                  0,                                  TEL_GPRS_RESIDUAL_BER_SUBSCRIBED      },
	{ 5,                                  2,                                  TEL_GPRS_RESIDUAL_BER_5102            },
	{ 1,                                  2,                                  TEL_GPRS_RESIDUAL_BER_1102            },
	{ 5,                                  3,                                  TEL_GPRS_RESIDUAL_BER_5103            },
	{ 4,                                  3,                                  TEL_GPRS_RESIDUAL_BER_4103            },
	{ 1,                                  3,                                  TEL_GPRS_RESIDUAL_BER_1103            },
	{ 1,                                  4,                                  TEL_GPRS_RESIDUAL_BER_1104            },
	{ 1,                                  5,                                  TEL_GPRS_RESIDUAL_BER_1105            },
	{ 1,                                  6,                                  TEL_GPRS_RESIDUAL_BER_1106            },
	{ 6,                                  8,                                  TEL_GPRS_RESIDUAL_BER_6108            },
	{ CI_PS_3G_ENUM_LOOKUP_END_ELEMENT,   CI_PS_3G_ENUM_LOOKUP_END_ELEMENT,   CI_PS_3G_ENUM_LOOKUP_END_ELEMENT  }
};

static const EnumParamLookup enumTrafficPriorityLookup[] =
{
	{ CI_PS_3G_SDU_TRAFFIC_PRIORITY_SUBSCRIBED,   TEL_GPRS_TRAFFIC_PRIORITY_LEVEL_SUBSCRIBED  },
	{ CI_PS_3G_SDU_TRAFFIC_PRIORITY_LEVEL_1,      TEL_GPRS_TRAFFIC_PRIORITY_LEVEL_1           },
	{ CI_PS_3G_SDU_TRAFFIC_PRIORITY_LEVEL_2,      TEL_GPRS_TRAFFIC_PRIORITY_LEVEL_2           },
	{ CI_PS_3G_SDU_TRAFFIC_PRIORITY_LEVEL_3,      TEL_GPRS_TRAFFIC_PRIORITY_LEVEL_3           },
	{ CI_PS_3G_ENUM_LOOKUP_END_ELEMENT,           CI_PS_3G_ENUM_LOOKUP_END_ELEMENT        }
};
#endif

int FdyMode=0;
int FdyLcdOnTimer=0, FdyLcdOffTimer=0,FdyRel8LcdOnTimer=0, FdyRel8LcdOffTimer=0;
unsigned char g_imsSrvStatus = 0;

extern BOOL  searchListByCid(unsigned char cid, DIRECTIPCONFIGLIST ** ppBuf);

extern char gPdpErrCauseBuf[];
extern char gPdpErrCauseBuf_1[];

extern int gIPv6_AddressFormat;
extern int gIPv6_AddressFormat_1;
extern int gIPv6_SubnetNotation;
extern int gIPv6_SubnetNotation_1;
extern int gIPv6_LeadingZeros;
extern int gIPv6_LeadingZeros_1;
extern int gIPv6_CompressZeros;
extern int gIPv6_CompressZeros_1;
extern int subnetmask_Str2Len(char* mask);
extern int subnetmask_str2len(char* mask);
extern int strcasecmp(const char *s1, const char *s2);

#ifdef SUPPORT_CM_DUSTER_DIALER
#ifdef LWIP_IPNETBUF_SUPPORT
extern void setWanMtu(int simID, int cid, int mtu, BOOL persist);
extern int getWanMtu(int simID, int cid);
extern BOOL cmuxIsAtpIndexForNetAT(UINT32 sAtpIndex);
extern UINT8 getMultiPdpSameApnFlag(void);
extern void setMultiPdpSameApnFlag(UINT8 flag);
#endif
#endif

static void convertCiStr2AtStr(char* data, UINT8 dataLength, char* outBuf)
{

    char tmp[3];
    UINT8 i = 0;
    outBuf[0] = '\0';

	memset(tmp, 0, sizeof(tmp));
    while(i < dataLength)
    {
		sprintf(tmp, "%02x", (UINT8)data[i++]);
        strcat(outBuf,tmp);
    }
}

/********************************************************************
 * Boolean ConvertPdpStr2addr (CiPsPdpAddr *input)
 *
 * Converts source address and subnet mask (sent by CI task) from string
 * in the form of "a1.a2.a3.a4.m1.m2.m3.m4" where a,m are between 0 and 255
 * (27.007 10.1.3) into an array of 8 octets where:
 * octet 0 = a1
 * octet 1 = a2
 * octet 2 = a3
 * octet 3 = a4
 * octet 4 = m1
 * octet 5 = m2
 * octet 6 = m3
 * octet 7 = m4
 * This is done so in order to use the same struct type SrcAddrSubnetMask
 * to carry both string and numeric values (both as array of Int8's)
 *
 * Returns TRUE  if conversion successful
 *         FALSE otherwise
 ********************************************************************/

/************************************************************************************
 *
 * PS related AT commands
 *
 *************************************************************************************/

/*Added by Michal Bukai*/


/*********************** Added By Michal Bukai **************************************
 * F@: ciEnterData - GLOBAL API for GCF AT+CGCMOD
 *
 */
RETURNCODE_T  ciModifyContext(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	/*
	 **  Set the result code to INITIAL_RETURN_CODE.  This allows
	 **  the indications to display the correct return code after the
	 **  AT Command is issued.
	 */
	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;
	INT32                                 cid=0;
	BOOL								  doAll=FALSE;

	/*
	 *  Put parser index into the variable
	 */
	UINT32 atHandle=0;
	TelAtParserID sAtpIndex = * (TelAtParserID *) arg_p;
	atHandle = MAKE_AT_HANDLE( sAtpIndex );
	*xid_p = atHandle;
	DBGMSG("ciModifyContext: atHandle = %d, op=%d", atHandle, op);

	/*
	 **  Check the format of the request.
	 */
	switch(op)
	{
		case TEL_EXT_TEST_CMD:    /* AT+CGCMOD=? */
		{
			/* Send the CI Request */
			ret = PS_GetGPRSActiveCidList(atHandle);

			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGCMOD= */
		{
			BOOL cmdValid = FALSE;
			//Get context identifier - multiple cids is not supported
			if ( getExtValue (parameter_values_p, 0, (int *)&cid, TEL_AT_CGCMOD_CID_VAL_MIN, TEL_AT_CGCMOD_CID_VAL_MAX, TEL_AT_CGCMOD_CID_VAL_DEFAULT) == TRUE )
			{
				if (( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
				{
					cmdValid = TRUE;
					if (parameter_values_p[0].is_default) /* Modify all active contexts */
						doAll = TRUE;
					else
					{
						doAll = FALSE;
						/* CI - SAC use cid range starting from 0 */ //Michl Bukai - NEED TOCHEK!!!!! al gprs cmand use this logic
						cid--;
					}

					//PDP ctc status is not being tested (define, active, data mode)  - let SAC check it
					/* Send the CI Request */
					ret= PS_ModifyGPRSContext(atHandle, cid, doAll);
				}
			}
			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
					break;
		}

		case TEL_EXT_GET_CMD:    /* AT+CGCMOD?  */
		case TEL_EXT_ACTION_CMD:   /* AT+CGCMOD */
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
/************************************************************************************
 * F@: ciReturnIp - GLOBAL API for GCF AT+GETIP -command
 *
 */
utlReturnCode_T  ciReturnIp( const utlAtParameterOp_T        op,
							 const char                      *command_name_p,
							 const utlAtParameterValue_P2c   parameter_values_p,
							 const size_t                    num_parameters,
							 const char                      *info_text_p,
							 unsigned int                    *xid_p,
							 void                            *arg_p)
{
	CiReturnCode ret = CIRC_FAIL;
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	INT32 index=0;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	*xid_p = atHandle;

	switch (op)
	{
		case TEL_EXT_SET_CMD: /*AT+GETIP=cid*/
			if ( getExtValue( parameter_values_p, 0, (int *)&index, TEL_AT_IP_INDEX_VAL_MIN, TEL_AT_IP_INDEX_VAL_MAX, TEL_AT_IP_INDEX_VAL_DEFAULT ) == TRUE )
			{
				index--;
				ret = PS_GetGPRSContextIP(atHandle, index);			}
		else
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}
	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}






#ifdef OPERATOR_VERIZON
/************************************************************************************
 * F@: ciPSAttach - GLOBAL API for MARVELL PRIVATE AT*CGATTC -command
 *
 */
RETURNCODE_T  ciPSAttachWithCause(            const utlAtParameterOp_T op,
				     const char                      *command_name_p,
				     const utlAtParameterValue_P2c parameter_values_p,
				     const size_t num_parameters,
				     const char                      *info_text_p,
				     unsigned int                    *xid_p,
				     void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	CiReturnCode ret = CIRC_FAIL;
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	INT32 cgatt;
	INT32 cause;
	UINT32 atHandle = MAKE_AT_HANDLE( *(TelAtParserID *)arg_p );

	*xid_p = atHandle;

	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	switch (op)
	{
		case TEL_EXT_TEST_CMD:    /* AT*CGATTC=? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*CGATTC: (0-1), (0-17)\r\n");
			break;
		}
		case TEL_EXT_SET_CMD:   /* AT*CGATTC=  */
		{
			/*
			**  Parse the state variable.
			*/
			if ( getExtValue(parameter_values_p, 0, (int *)&cgatt, TEL_AT_CGATTC_0_STATE_VAL_MIN, TEL_AT_CGATTC_0_STATE_VAL_MAX, TEL_AT_CGATTC_0_STATE_VAL_DEFAULT) == TRUE )
			{
				if (getExtValue(parameter_values_p, 1, (int *)&cause, TEL_AT_CGATTC_1_CAUSE_VAL_MIN, TEL_AT_CGATTC_1_CAUSE_VAL_MAX, TEL_AT_CGATTC_1_CAUSE_VAL_DEFAULT) == TRUE )
				{
					/*
					**  Check if this is an attach or detach request.
					*/
					switch (cgatt)
					{
						case ATCI_GPRS_ATTACH_REQ:       /* perform GPRS attach */
						{
							ret = PS_SetGPRSAttached(atHandle, TRUE, (CiPsAttachStateCause)cause);
							break;
						}

						case ATCI_GPRS_DETACH_REQ:       /* perform GPRS detach and flag all the CIDs as inactive */
						{
							ret = PS_SetGPRSAttached(atHandle, FALSE, (CiPsAttachStateCause)cause);
							break;
						}

						default:
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
							break;
					}       /* switch */
				}
				else
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);

			}
			else
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}
		case TEL_EXT_GET_CMD:    /* AT*CGATTC?  */
		case TEL_EXT_ACTION_CMD:   /* AT*CGATTC   */
		default:
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}
#endif







/************************************************************************************
 * F@: getAtQosParams - extracts the Qos attributes from AT command line string
 *
 */
BOOL getAtQosParams ( const utlAtParameterValue_P2c   parameter_values_p, int index, CiPsQosProfile *qosProfile )
{
	BOOL      result = TRUE;
	INT32        precedence=0;         /* Precedence class */
	INT32        delay=0;              /* Delay class */
	INT32        reliability=0;        /* Reliability class */
	INT32        peak=0;               /* Peak throughput */
	INT32        mean=0;               /* Mean throughput */

	/* get the PRECEDENCE */
	if (getExtValue (parameter_values_p, index++, (int *)&precedence, TEL_AT_CGQMIN_1_PRECEDENCE_VAL_MIN, TEL_AT_CGQMIN_1_PRECEDENCE_VAL_MAX, TEL_AT_CGQMIN_1_PRECEDENCE_VAL_DEFAULT) == TRUE)
	{
		if ((precedence == ATCI_DEFAULT_PARAM_VALUE) || (precedence == 0))
		{
			/* no parameter, or zero, supplied, set up the default qos value */
			qosProfile->precedence = TEL_GPRS_PRECED_CLASS_SUBSCRIBED;
		}
		else
		{
			if (precedence <= TEL_GPRS_PRECED_CLASS_3)
			{
				qosProfile->precedence = (UINT8)precedence;
			}
			else
			{
				result = FALSE;
			}
		}
	}
	else
	{
		result = FALSE;
	}

	/* get the DELAY */
	if ( result == TRUE )
	{
		if (getExtValue (parameter_values_p, index++, (int *)&delay, TEL_AT_CGQMIN_2_DELAY_VAL_MIN, TEL_AT_CGQMIN_2_DELAY_VAL_MAX, TEL_AT_CGQMIN_2_DELAY_VAL_DEFAULT) == TRUE)
		{
			if ((delay == ATCI_DEFAULT_PARAM_VALUE) || (delay == 0))
			{
				/* no parameter, or zero, supplied, set up the default qos value */
				qosProfile->delay = TEL_GPRS_DELAY_CLASS_SUBSCRIBED;
			}
			else
			{
				if ( delay <= TEL_GPRS_DELAY_CLASS_4 )
				{
					qosProfile->delay = (UINT8)delay;
				}
				else
				{
					result = FALSE;
				}
			}
		}
		else
		{
			result = FALSE;
		}
	}

	/* get the RELIABILITY */
	if ( result == TRUE )
	{
		if (getExtValue (parameter_values_p, index++, (int *)&reliability, TEL_AT_CGQMIN_3_RELIABILITY_VAL_MIN, TEL_AT_CGQMIN_3_RELIABILITY_VAL_MAX, TEL_AT_CGQMIN_3_RELIABILITY_VAL_DEFAULT) == TRUE)
		{
			if ((reliability == ATCI_DEFAULT_PARAM_VALUE) || (reliability == 0))
			{
				/* no parameter, or zero, supplied, set up the default qos value */
				qosProfile->reliability = TEL_GPRS_RELIAB_CLASS_SUBSCRIBED;
			}
			else
			{
				if (reliability <= TEL_GPRS_RELIAB_CLASS_5)
				{
					qosProfile->reliability = (UINT8)reliability;
				}
				else
				{
					result = FALSE;
				}
			}
		}
		else /* error reading supplied parameter */
		{
			result = FALSE;
		}
	}

	/* get the PEAK */
	if ( result == TRUE )
	{
		if ( getExtValue (parameter_values_p, index++, (int *)&peak, TEL_AT_CGQMIN_4_PEAK_VAL_MIN, TEL_AT_CGQMIN_4_PEAK_VAL_MAX, TEL_AT_CGQMIN_4_PEAK_VAL_DEFAULT) == TRUE )
		{
			if ((peak == ATCI_DEFAULT_PARAM_VALUE) || (peak == 0))
			{
				/* no parameter, or zero, supplied */
				/* set up the default qos value */
				qosProfile->peak = TEL_GPRS_PEAK_THRPT_SUBSCRIBED;
			}
			else
			{
				if (peak <= TEL_GPRS_PEAK_THRPT_UPTO_256KOCT)
				{
					qosProfile->peak = (UINT8)peak;
				}
				else
				{
					result = FALSE;
				}
			}
		}
		else /* error reading supplied parameter */
		{
			result = FALSE;
		}
	}

	/* get the MEAN */
	if ( result == TRUE )
	{
		if (getExtValue (parameter_values_p, index++, (int *)&mean, TEL_AT_CGQMIN_5_MEAN_VAL_MIN, TEL_AT_CGQMIN_5_MEAN_VAL_MAX, TEL_AT_CGQMIN_5_MEAN_VAL_DEFAULT) == TRUE)
		{
			if ((mean == ATCI_DEFAULT_PARAM_VALUE) || (mean == 0))
			{
				/* no parameter, or zero, supplied, set up the default qos value */
				qosProfile->mean = TEL_GPRS_MEAN_THRPT_SUBSCRIBED;
			}
			else
			{
				if ((mean <= TEL_GPRS_MEAN_THRPT_50M_OPH) || (mean == TEL_GPRS_MEAN_THRPT_BEST_EFFORT))
				{
					qosProfile->mean = (UINT8)mean;
				}
				else
				{
					result = FALSE;
				}
			}
		}
		else /* error reading supplied parameter */
		{
			result = FALSE;
		}
	}

	return (result);
}

#ifndef LTEONLY_THIN

/************************************************************************************
 * F@: ciSetMinQOS - GLOBAL API for GCF AT+CGQMIN -command
 *
 */
RETURNCODE_T  ciSetMinQOS(            const utlAtParameterOp_T        op,
									  const char                      *command_name_p,
									  const utlAtParameterValue_P2c   parameter_values_p,
									  const size_t                    num_parameters,
									  const char                      *info_text_p,
									  unsigned int                    *xid_p,
									  void                            *arg_p)
{
	RETURNCODE_T                   rc = INITIAL_RETURN_CODE;
	CiReturnCode                   ret = CIRC_SUCCESS;
	INT32                          cid=0;
	CiPsQosProfile          minQosProfile;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	memset(&minQosProfile, 0, sizeof(minQosProfile));

	DBGMSG("ciSetMinQOS: atHandle = %d, op=%d", atHandle, op);

	switch(op)
	{
		case TEL_EXT_TEST_CMD:    /* AT+CGQMIN=? */
		{
			ret = PS_GetGprsCapsQos(atHandle, TRUE);
			break;
		}

		case TEL_EXT_GET_CMD:    /* AT+CGQMIN?  */
		{
			ret = PS_GetQualityOfServiceList(atHandle, TRUE);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGQMIN= */
		{
			BOOL cmdValid = FALSE;
			/*
			**  Extract the arguments starting with the CID.
			*/
			if (( getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGQMIN_0_CID_VAL_MIN, TEL_AT_CGQMIN_0_CID_VAL_MAX, TEL_AT_CGQMIN_0_CID_VAL_DEFAULT) == TRUE ) &&
			    ( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
			{
				cid--;
				if ( getAtQosParams( parameter_values_p, 1, &minQosProfile ) == TRUE )
				{
					cmdValid = TRUE;
					ret = PS_SetQualityOfService(atHandle, cid, &minQosProfile, TRUE);
				}
			}
			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGQMIN */
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciSetReqQOS - GLOBAL API for GCF AT+CGQREQ  -command
 *
 */
RETURNCODE_T  ciSetReqQOS(            const utlAtParameterOp_T        op,
									  const char                      *command_name_p,
									  const utlAtParameterValue_P2c   parameter_values_p,
									  const size_t                    num_parameters,
									  const char                      *info_text_p,
									  unsigned int                    *xid_p,
									  void                            *arg_p)
{
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	INT32 cid=0;
	CiPsQosProfile reqQosProfile;
	UINT32 atHandle = MAKE_AT_HANDLE( *(TelAtParserID *)arg_p );

	*xid_p = atHandle;

	DBGMSG("ciSetReqQOS: atHandle = %d, op=%d", atHandle, op);
	memset(&reqQosProfile, 0, sizeof(reqQosProfile));

	switch(op)
	{
		case TEL_EXT_TEST_CMD:    /* AT+CGQREQ=? */
		{
			ret = PS_GetGprsCapsQos(atHandle, FALSE);
			break;
		}

		case TEL_EXT_GET_CMD:    /* AT+CGQREQ?  */
		{
			ret = PS_GetQualityOfServiceList(atHandle, FALSE);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGQREQ= */
		{
			BOOL cmdValid = FALSE;
			/*
			 **  Extract the arguments starting with the CID.
			 */
			if (( getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGQREQ_0_CID_VAL_MIN, TEL_AT_CGQREQ_0_CID_VAL_MAX, TEL_AT_CGQREQ_0_CID_VAL_DEFAULT) == TRUE ) &&
					( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
			{
				cid--;
				if ( getAtQosParams( parameter_values_p, 1, &reqQosProfile ) == TRUE )
				{
					cmdValid = TRUE;
					ret = PS_SetQualityOfService(atHandle, cid, &reqQosProfile, FALSE);
				}

			}
			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
					break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGQREQ */
		default:
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

#endif

#ifdef PPP_ENABLE
RETURNCODE_T ciChapAuthReq(            const utlAtParameterOp_T op,
					const char                      *command_name_p,
					const utlAtParameterValue_P2c parameter_values_p,
					const size_t num_parameters,
					const char                      *info_text_p,
					unsigned int                    *xid_p,
					void                            *arg_p)
{
	UNUSEDPARAM(command_name_p);
	UNUSEDPARAM(num_parameters);
	UNUSEDPARAM(info_text_p);
	UNUSEDPARAM(arg_p);
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	CiPsPrimChapAuthenticateReq authReq;

	CHAR challengeVal[CI_MAX_CI_STRING_EXT_LENGTH + ATCI_NULL_TERMINATOR_LENGTH] = { '\0' };
	CHAR responseVal[CI_MAX_CI_STRING_EXT_LENGTH + ATCI_NULL_TERMINATOR_LENGTH] = { '\0' };
	INT32 cid;
	INT16 challengeLen, responseLen;

	UINT32 atHandle = MAKE_AT_HANDLE( *(TelAtParserID *)arg_p );
	*xid_p = atHandle;
	BOOL cmdValid = FALSE;

	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);
	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*CHAPAUTH?  */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
		break;
	}
	case TEL_EXT_TEST_CMD:	 /* AT*CHAPAUTH=?  */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*CHAPAUTH: <cid>[,<challenge>[,<response>]]\r\n");
		break;
	}
	case TEL_EXT_SET_CMD:      /* AT*CHAPAUTH= */
	{
		if( getExtValue( parameter_values_p, 0, (int *)&cid, TEL_AT_AUTH_0_CID_VAL_MIN + 1, 0xFF + 1, TEL_AT_AUTH_0_CID_VAL_DEFAULT ) == TRUE )
		{
			cid--;
			if ( getExtString(parameter_values_p, 1, &challengeVal[0], CI_MAX_CI_STRING_EXT_LENGTH, &challengeLen, NULL) == TRUE )
			{
				if ( getExtString(parameter_values_p, 2, &responseVal[0], CI_MAX_CI_STRING_EXT_LENGTH, &responseLen, NULL) == TRUE )
				{
					cmdValid = TRUE;
					//ATDBGMSG(atcmdsrv0,96,"%s: cid %d\n", __FUNCTION__, cid);
					//ATDBGMSG(atcmdsrv0,97,"%s: challengeVal %s\n", __FUNCTION__, challengeVal);
					//ATDBGMSG(atcmdsrv0,98,"%s: responseVal %s\n", __FUNCTION__, responseVal);

					ret = PS_SendChapAuthReq(atHandle, cid, challengeVal, responseVal);
				}
			}

		}
		if(!cmdValid)
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
		break;
	}
	case TEL_EXT_ACTION_CMD:   /* AT*CHAPAUTH */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
		break;
	}
	default:
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
		break;
	}

	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#endif



/************************************************************************************
 * F@: Encode3GBitrate - Encode bitrate value for 3G QoS as per 3GPP TS 24.008
 * Notes       : See 3GPP TS 24.008/V3.11.0, Table 10.5.156 for
 *               the coding scheme.
 *               This function is used for the Maximum and Guaranteed
 *               Bit Rate (both Uplink and Downlink) parameters.
 */
UINT8 Encode3GBitrate( INT32 rawValue )
{
	UINT8 codedValue = 0x00;    /* Default = Subscribed Value */

	if (rawValue <= 0)
	{
	  codedValue = 0;
	}
	else if( rawValue >= 1 && rawValue <= 63 )
	{
		/* 1kbps..63kbps (1kbps steps) -> 0x01..0x3f */
		codedValue = (UINT8) rawValue;
	}
	else if( rawValue >= 64 && rawValue <= 568 )
	{
		/* 64kbps..568kbps (8kbps steps) -> 0x40..0x7f */
		codedValue = (UINT8) ( ( rawValue - 64 ) / 8 ) + 0x40;
	}
	else if( rawValue >= 576 && rawValue <= 8640 )
	{
		/* 576kbps..8640kbps (64kbps steps) -> 0x80..0xfe */
		codedValue = (UINT8) ( ( rawValue - 576 ) / 64 ) + 0x80;
	}
	else if( rawValue <= 16000 )
	{
		/* 8700kbps..16000kbps (100 kbps steps) ->	0x01..0x4a*/
		codedValue = (rawValue / 100) - 86;
	}
	else if (rawValue <= 128*1000)
	{
	    /* 17Mbps..128Mbps  (1 Mbps steps) ->  0x4b..0xba*/
		codedValue = (rawValue / 1000) + 58;
	}
	else if (rawValue <= 256*1000)
	{
          /* 130Mbps..256Mbps  (1 Mbps steps) ->  0xbb..0xfa*/
		codedValue = (rawValue / 2000) + 122;

	}
	/* Out-of-range values are coded as default (set above) */

	return( codedValue );
}

/* Added by Daniel for LTE PC AT command server 20120313, begin */
/************************************************************************************
 * F@: Encode4GBitrate - Encode bitrate value for 4G QoS as per 3GPP TS 24.008
 * Notes       : See 3GPP TS 24.008/V3.11.0, Table 10.5.156 for
 *               the coding scheme.
 *               This function is used for the Maximum and Guaranteed
 *               Bit Rate (both Uplink and Downlink) parameters.
 */
UINT16 Encode4GBitrate( UINT32 rawValue )
{
	UINT16 codedValue = 0x00;    /* Default = Subscribed Value */

	if( rawValue >= 1 && rawValue <= 63 )
	{
		/* 1kbps..63kbps (1kbps steps) -> 0x01..0x3f */
		codedValue = (UINT16)rawValue;
	}
	else if( rawValue >= 64 && rawValue <= 568 )
	{
		/* 64kbps..568kbps (8kbps steps) -> 0x40..0x7f */
		codedValue = (UINT16)((rawValue - 64) / 8) + 0x40;
	}
	else if( rawValue >= 576 && rawValue <= 8640 )
	{
		/* 576kbps..8640kbps (64kbps steps) -> 0x80..0xfe */
		codedValue = (UINT16)((rawValue - 576) / 64) + 0x80;
	}
	else if(rawValue >= 8700 && rawValue <= 16000)
	{
		/* 8700kbps..16000kbps (100kbps steps) -> 0x00FE..0x4AFE */
		codedValue = (UINT16)(((rawValue - 8600) / 100) << 8) + 0xFE;
	}
	else if(rawValue >= 17000 && rawValue <= 128000)
	{
		/* 17Mbps..128Mbps (1Mkbps steps) -> 0x4BFE..0xBAFE */
		codedValue = (UINT16)(((rawValue - 16000) / 1000) << 8) + 0x4BFE;
	}
	else if(rawValue >= 130000 && rawValue <= 256000)
	{
		/* 130Mbps..256Mbps (2Mkbps steps) -> 0xBBFE..0xFAFE */
		codedValue = (UINT16)(((rawValue - 128000) / 2000) << 8) + 0xBBFE;
	}

	/* Out-of-range values are coded as default (set above) */

	return( codedValue );
}

/* Added by Daniel for LTE PC AT command server 20120313, end */

/************************************************************************************
 * F@: Encode3GMaxSduSize - Encodes 3G QoS Maximum SDU Size parameter value
 * Notes       : See 3GPP TS 24.008/V3.11.0, Table 10.5.156 for
 *               the coding scheme.
 *
 */
UINT8 Encode3GMaxSduSize( INT16 rawValue )
{
	UINT8 codedValue = 0x00;    /* Default = Subscribed Value */

	if( rawValue >= 10 && rawValue <= 1500 )
	{
		/* 10..1500 octets (10-octet steps) -> 0x01..0x96 */
		codedValue = (UINT8) ( rawValue / 10 );
	}
	else if( rawValue == 1502 )
	{
		/* 1502 octets -> 0x97 */
		codedValue = (UINT8) 0x97;
	}
	else if( rawValue == 1510 )
	{
		/* 1510 octets -> 0x98 */
		codedValue = (UINT8) 0x98;
	}
	else if( rawValue == 1520 )
	{
		/* 1520 octets -> 0x99 */
		codedValue = (UINT8) 0x99;
	}

	/* Out-of-range values are coded as default (set above) */

	return( codedValue );
}


/************************************************************************************
 * F@:  Encode3GTransDelay - Encodes 3G QoS Transfer Delay parameter value
 * Notes       : See 3GPP TS 24.008/V3.11.0, Table 10.5.156 for
 *               the coding scheme.
 *
 */
UINT8 Encode3GTransDelay( INT16 rawValue )
{
	UINT8 codedValue = 0x00;    /* Default = Subscribed Value */

	if( rawValue >= 10 && rawValue <= 150 )
	{
		/* 10ms..150ms (10ms steps) -> 0x01..0x0f */
		codedValue = (UINT8) ( rawValue / 10 );
	}
	else if( rawValue >= 200 && rawValue <= 950 )
	{
		/* 200ms..950ms (50ms steps) -> 0x10..0x1f */
		codedValue = (UINT8) ( ( rawValue - 200 ) / 50 ) + 0x10;
	}
	else if( rawValue >= 1000 && rawValue <= 4000 )
	{
		/* 1000ms..4000ms (100ms steps) -> 0x20..0x3e */
		codedValue = (UINT8) ( ( rawValue - 1000 ) / 100 ) + 0x20;
	}

	/* Out-of-range values are coded as default (set above) */

	return( codedValue );
}

/************************************************************************************
 * F@: at3GQOSisExtensionEncodeBitRate -
 * Notes
 *
 *
 */
static BOOL at3GQOSisExtensionEncodeBitRate( INT32 rawValue)
{
  BOOL isExtension = FALSE;


  if( rawValue >= 0 && rawValue <= 8640 )
  {
	  isExtension = FALSE;

  }
  else if (rawValue >8640 &&rawValue <=  256*1000)
  {
	isExtension = TRUE;
  }
  return( isExtension );
}

#ifndef LTEONLY_THIN

#if defined (UPGRADE_R99)

/************************************************************************************
 * F@: getAt3GQosParams - extracts the 3G Qos attributes from AT command line string
 *
 */
BOOL getAt3GQosParams ( const utlAtParameterValue_P2c   parameter_values_p, CiPs3GQosProfile *qosProfile )
{

	INT32                           trafficPriority=0;
	INT32                           transferDelay=0;
	INT32                           trafficClass=0;
	INT32                           maxBitRateUplink=0;
	INT32                           maxBitRateDownlink=0;
	INT32                           guaranteedBitRateUplink=0;
	INT32                           guaranteedBitRateDownlink=0;
	INT32                           deliveryOrder=0;
	INT32                           sduErrorRatio=0;
	INT32                           ratioBER=0;
	INT32                           maxSduSize=0;
	INT32                           errSdu=0;
	BOOL                           IsExtension=FALSE;
	INT32                           IsExtensionbitmap=0;
	INT32                           SourceStatisticDescriptor=0;
	INT32                           SignallingIndication=0;
	BOOL                         cmdValid = FALSE;


	/*
	 **  Get Traffic class....
	 */
	if ( getExtValue(parameter_values_p, 1, (int *)&trafficClass, TEL_AT_CGEQREQ_1_TRAFFIC_VAL_MIN, TEL_AT_CGEQREQ_1_TRAFFIC_VAL_MAX, TEL_AT_CGEQREQ_1_TRAFFIC_VAL_DEFAULT) == TRUE )
	{
		/*
		 **  Get Bitrate U/L....
		 */
		cmdValid = getExtValue( parameter_values_p, 2, (int *)&maxBitRateUplink,
								TEL_AT_CGEQREQ_2_MAX_UL_VAL_MIN, TEL_AT_CGEQREQ_2_MAX_UL_VAL_MAX, TEL_AT_CGEQREQ_2_MAX_UL_VAL_DEFAULT);

		IsExtension = at3GQOSisExtensionEncodeBitRate(maxBitRateUplink);
		if(IsExtension == TRUE)
		{
			IsExtensionbitmap|= (1 << CI_PS_3G_MAX_BIT_RATE_FOR_UL);
		}

		if (( cmdValid == TRUE ) &&
	 		((( maxBitRateUplink > 64 && maxBitRateUplink < 568 ) && ( maxBitRateUplink%8 != 0 )) ||
	 		 (( maxBitRateUplink > 568 && maxBitRateUplink < 8640 ) && ( maxBitRateUplink%64 != 0 ))||
	 		(( maxBitRateUplink > 8640 && maxBitRateUplink < 16000 ) && ( maxBitRateUplink%100 != 0 ))||
	 		(( maxBitRateUplink > 16000 && maxBitRateUplink < 128000 ) && ( maxBitRateUplink%1000 != 0 ))||
	 		(( maxBitRateUplink > 128000 && maxBitRateUplink < 256000 ) && ( maxBitRateUplink%2000 != 0 ))))
		{
			/*
			 **  This is an invalid value.
			 */
			cmdValid = FALSE;
		}
		else
		{
			maxBitRateUplink = (INT32)Encode3GBitrate( (INT32)maxBitRateUplink );
		}

		/*
		 **  Get Bitrate D/L....
		 */
		if ( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 3, (int *)&maxBitRateDownlink,
					TEL_AT_CGEQREQ_3_MAX_DL_VAL_MIN, TEL_AT_CGEQREQ_3_MAX_DL_VAL_MAX, TEL_AT_CGEQREQ_3_MAX_DL_VAL_DEFAULT);

			/*encode max DL rate and mask its extension bit if needed*/
			IsExtension = at3GQOSisExtensionEncodeBitRate(maxBitRateDownlink);

			if(IsExtension == TRUE)
			{
			  IsExtensionbitmap |= (1 << CI_PS_3G_MAX_BIT_RATE_FOR_DL);
			}

			if (( cmdValid == TRUE ) &&
				((( maxBitRateDownlink > 64 && maxBitRateDownlink < 568 ) && ( maxBitRateDownlink%8 != 0 )) ||
				 (( maxBitRateDownlink > 568 && maxBitRateDownlink < 8640 ) && ( maxBitRateDownlink%64 != 0 ))||
				(( maxBitRateDownlink > 8640 && maxBitRateDownlink < 16000 ) && ( maxBitRateDownlink%100 != 0 ))||
				(( maxBitRateDownlink > 16000 && maxBitRateDownlink < 128000 ) && ( maxBitRateDownlink%1000 != 0 ))||
				(( maxBitRateDownlink > 128000 && maxBitRateDownlink < 256000 ) && ( maxBitRateDownlink%2000 != 0 ))))
			{
				/*
				 **  This is an invalid value.
				 */
				cmdValid = FALSE;
			}
			else
			{
				maxBitRateDownlink = (INT32)Encode3GBitrate( (INT32)maxBitRateDownlink );
			}
		}

		/*
		 **  Get Guaranteed Bitrate U/L....
		 */
		if ( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 4, (int *)&guaranteedBitRateUplink,
									TEL_AT_CGEQREQ_4_GUA_UL_VAL_MIN, TEL_AT_CGEQREQ_4_GUA_UL_VAL_MAX, TEL_AT_CGEQREQ_4_GUA_UL_VAL_DEFAULT );


			/*encode guaranteed UL rate and mask its extension bit if needed*/
			IsExtension = at3GQOSisExtensionEncodeBitRate(guaranteedBitRateUplink);
			if(IsExtension == TRUE)
			{
			  IsExtensionbitmap |= (1 << CI_PS_3G_GUARNTEED_BIT_RATE_FOR_UL);
			}

			if (( cmdValid == TRUE ) &&
				((( guaranteedBitRateUplink > 64 && guaranteedBitRateUplink < 568 ) && ( guaranteedBitRateUplink%8 != 0 )) ||
				 (( guaranteedBitRateUplink > 568 && guaranteedBitRateUplink < 8640 ) && ( guaranteedBitRateUplink%64 != 0 ))||
				(( guaranteedBitRateUplink > 8640 && guaranteedBitRateUplink < 16000 ) && ( guaranteedBitRateUplink%100 != 0 ))||
				(( guaranteedBitRateUplink > 16000 && guaranteedBitRateUplink < 128000 ) && ( guaranteedBitRateUplink%1000 != 0 ))||
				(( guaranteedBitRateUplink > 128000 && guaranteedBitRateUplink < 256000 ) && ( guaranteedBitRateUplink%2000 != 0 ))))
			{
				/*
				 **  This is an invalid value.
				 */
				cmdValid = FALSE;
			}
			else
			{
				guaranteedBitRateUplink = (INT32)Encode3GBitrate( (INT32)guaranteedBitRateUplink );
			}
		}

		/*
		 **  Get Guaranteed Bitrate D/L....
		 */
		if ( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 5, (int *)&guaranteedBitRateDownlink,
					TEL_AT_CGEQREQ_5_GUA_DL_VAL_MIN, TEL_AT_CGEQREQ_5_GUA_DL_VAL_MAX, TEL_AT_CGEQREQ_5_GUA_DL_VAL_DEFAULT );

			/*encode guaranteed DL rate and mask its extension bit if needed*/
			IsExtension = at3GQOSisExtensionEncodeBitRate(guaranteedBitRateDownlink);
			if(IsExtension == TRUE)
			{
			  IsExtensionbitmap |= (1 << CI_PS_3G_GUARNTEED_BIT_RATE_FOR_DL);
			}

			if (( cmdValid == TRUE ) &&
				((( guaranteedBitRateDownlink > 64 && guaranteedBitRateDownlink < 568 ) && ( guaranteedBitRateDownlink%8 != 0 )) ||
				 (( guaranteedBitRateDownlink > 568 && guaranteedBitRateDownlink < 8640 ) && ( guaranteedBitRateDownlink%64 != 0 ))||
				(( guaranteedBitRateDownlink > 8640 && guaranteedBitRateDownlink < 16000 ) && ( guaranteedBitRateDownlink%100 != 0 ))||
				(( guaranteedBitRateDownlink > 16000 && guaranteedBitRateDownlink < 128000 ) && ( guaranteedBitRateDownlink%1000 != 0 ))||
				(( guaranteedBitRateDownlink > 128000 && guaranteedBitRateDownlink < 256000 ) && ( guaranteedBitRateDownlink%2000 != 0 ))))
			{
				/*
				 **  This is an invalid value.
				 */
				cmdValid = FALSE;
			}
			else
			{
				guaranteedBitRateDownlink = (INT32)Encode3GBitrate( (INT32)guaranteedBitRateDownlink );
			}
		}

		/*
		 **  Get Delivery Order....
		 */
		if( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 6, (int *)&deliveryOrder,
									TEL_AT_CGEQREQ_6_DELORDER_VAL_MIN, TEL_AT_CGEQREQ_6_DELORDER_VAL_MAX, TEL_AT_CGEQREQ_6_DELORDER_VAL_DEFAULT );
		}

		/*
		 **  Get max SDU size....
		 */
		if( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 7, (int *)&maxSduSize,
									TEL_AT_CGEQREQ_7_MAXSDU_VAL_MIN, TEL_AT_CGEQREQ_7_MAXSDU_VAL_MAX, TEL_AT_CGEQREQ_7_MAXSDU_VAL_DEFAULT );

			if (( cmdValid == TRUE ) &&
					(( maxSduSize > 0 && maxSduSize < 10 ) ||
					 (( maxSduSize > 10 && maxSduSize < 1500) && ( maxSduSize%10 != 0 )) ||
					 ( maxSduSize == 1501 ) ||
					 ( maxSduSize > 1502 && maxSduSize < 1510 ) ||
					 ( maxSduSize > 1510 && maxSduSize < 1520 )))
			{
				/*
				 **  This is an invalid value.
				 */
				cmdValid = FALSE;
			}
			else
			{
				maxSduSize = (INT32)Encode3GMaxSduSize( (INT16)maxSduSize );
			}
		}

		/*
		 **  Get SDU error ratio....
		 */
		if ( cmdValid == TRUE )
		{
			cmdValid = getErrorRatioParam( parameter_values_p, 8, (ErrorRatioLookup *)errorRatioBER,
										   TEL_AT_CGEQREQ_8_SDUERR_VAL_DEFAULT, &sduErrorRatio );
		}

		if ( cmdValid == TRUE )
		{
			cmdValid = getErrorRatioParam( parameter_values_p, 9, (ErrorRatioLookup *)errorRatioBER,
										   TEL_AT_CGEQREQ_9_RBERR_VAL_DEFAULT, &ratioBER );
		}

		/*
		 **  Get delivery of erroneous SDUs....
		 */
		if( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 10, (int *)&errSdu,
									TEL_AT_CGEQREQ_10_DERSDU_VAL_MIN, TEL_AT_CGEQREQ_10_DERSDU_VAL_MAX, TEL_AT_CGEQREQ_10_DERSDU_VAL_DEFAULT );
		}

		/*
		 **  Get transfer delay....
		 */
		if( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 11, (int *)&transferDelay, TEL_AT_CGEQREQ_11_TRDELAY_VAL_MIN, TEL_AT_CGEQREQ_11_TRDELAY_VAL_MAX, TEL_AT_CGEQREQ_11_TRDELAY_VAL_DEFAULT );

			if ( cmdValid == TRUE )
			{
				if ((( transferDelay > 0 && transferDelay < 150 ) && ( transferDelay%10 != 0 )) ||
						(( transferDelay > 150 && transferDelay < 950 ) && ( transferDelay%50 != 0 )) ||
						((transferDelay > 950 && transferDelay < 4000 ) && ( transferDelay%100 != 0 )))
				{
					cmdValid = FALSE;
				}
				else
				{
					transferDelay = (INT32)Encode3GTransDelay( (INT16)transferDelay );
				}
			}
		}

		/*
		 **  Get traffic handling priority....
		 */
		if( cmdValid == TRUE )
		{
			cmdValid = atParamToCiEnum( parameter_values_p, 12, (int *)&trafficPriority,
										TEL_AT_CGEQREQ_12_PRIORITY_VAL_DEFAULT,
										(EnumParamLookup *)enumTrafficPriorityLookup );
		}

		/*
		**  Get SourceStatisticDescriptor
		*/
		if( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 13, (int *)&SourceStatisticDescriptor,
					TEL_AT_CGEQREQ_13_SOURCEDESCRIPTOR_VAL_MIN, TEL_AT_CGEQREQ_13_SOURCEDESCRIPTOR_VAL_MAX, TEL_AT_CGEQREQ_13_SOURCEDESCRIPTOR_VAL_DEFAULT);
		}

		/*
		**  Get SignallingIndication
		*/
		if( cmdValid == TRUE )
		{
			cmdValid = getExtValue( parameter_values_p, 14, (int *)&SignallingIndication,
					TEL_AT_CGEQREQ_14_SIGNALINDICATION_VAL_MIN, TEL_AT_CGEQREQ_14_SIGNALINDICATION_VAL_MAX, TEL_AT_CGEQREQ_14_SIGNALINDICATION_VAL_DEFAULT);
		}

	}  /* if ( _atParamToCiEnum( parameter_values_p, X, &trafficClass, */


	if( cmdValid == TRUE )
	{
		/*
		 **  Set the CI request parameters.
		 */
		qosProfile->trafficClass 	= (CiPs3GTrafficClass)trafficClass;
		qosProfile->deliveryOrder	= (CiPs3GDlvOrder)deliveryOrder;
		qosProfile->sduErrRatio		= (CiPs3GSduErrorRatio)sduErrorRatio;
		qosProfile->resBER			= (CiPs3GResidualBer)ratioBER;
		qosProfile->deliveryOfErrSdu = (CiPs3GDlvErrorSdu)errSdu;
		qosProfile->thPriority		= (CiPs3GTrafficPriority)trafficPriority;
		qosProfile->maxSduSize		= (UINT8)maxSduSize;
		qosProfile->transDelay		= (UINT8)transferDelay;
		qosProfile->maxULRate		= (UINT16)maxBitRateUplink;
		qosProfile->maxDLRate		= (UINT16)maxBitRateDownlink;
		qosProfile->guaranteedULRate = (UINT16)guaranteedBitRateUplink;
		qosProfile->guaranteedDLRate = (UINT16)guaranteedBitRateDownlink;
		qosProfile->IsExtension = (UINT32)IsExtensionbitmap;
		qosProfile->SourceStatisticDescriptor = (CiPsSourceStatisticDescriptorType)SourceStatisticDescriptor;
		qosProfile->SignallingIndication = (CiPsSignallingIndicationType)SignallingIndication;
	}

	return cmdValid;
}



/************************************************************************************
 * F@: ciSet3GQOS - GLOBAL API for GCF AT+CGEQREQ  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciSet3GQOS(            const utlAtParameterOp_T        op,
									 const char                      *command_name_p,
									 const utlAtParameterValue_P2c   parameter_values_p,
									 const size_t                    num_parameters,
									 const char                      *info_text_p,
									 unsigned int                    *xid_p,
									 void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	BOOL                         cmdValid = FALSE;
	INT32                           cid=0;

	CiPs3GQosProfile         qosProfile;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	memset(&qosProfile, 0, sizeof(qosProfile));

	DBGMSG("ciSet3GQOS: atHandle = %d, op=%d", atHandle, op);

	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGEQREQ?  */
		{
			ret = PS_Get3GQualityOfServiceList(atHandle, CI_PS_3G_QOSTYPE_REQ);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGEQREQ=? */
		{
			ret = PS_Get3GCapsQos(atHandle, CI_PS_3G_QOSTYPE_REQ);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGEQREQ= */
		{
			/*
			 **  Extract the arguments starting with the CID.
			 */
			if (( getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGEQREQ_0_CID_VAL_MIN, TEL_AT_CGEQREQ_0_CID_VAL_MAX, TEL_AT_CGEQREQ_0_CID_VAL_DEFAULT) == TRUE )  &&
	    			( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
			{

				cmdValid = getAt3GQosParams(parameter_values_p, &qosProfile);

				if( cmdValid == TRUE )
					ret = PS_Set3GQualityOfService(atHandle, cid-1, &qosProfile, CI_PS_3G_QOSTYPE_REQ);
				else
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );

			}       /*  if (( getExtValue(parameter_values_p, X, &cid */
			else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGEQREQ */
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciSet3GminQOS - GLOBAL API for GCF AT+CGEQMIN  -command
 *
 */
RETURNCODE_T  ciSet3GminQOS(            const utlAtParameterOp_T        op,
										const char                      *command_name_p,
										const utlAtParameterValue_P2c   parameter_values_p,
										const size_t                    num_parameters,
										const char                      *info_text_p,
										unsigned int                    *xid_p,
										void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	BOOL                         cmdValid = FALSE;
	INT32                           cid=0;

	CiPs3GQosProfile         qosProfile;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	memset(&qosProfile, 0, sizeof(qosProfile));

	DBGMSG("ciSet3GminQOS: atHandle = %d, op=%d", atHandle, op);

	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGEQMIN?  */
		{
			ret = PS_Get3GQualityOfServiceList(atHandle, CI_PS_3G_QOSTYPE_MIN);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGEQMIN=? */
		{
			ret = PS_Get3GCapsQos(atHandle, CI_PS_3G_QOSTYPE_MIN);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGEQMIN= */
		{
			/*
			**  Extract the arguments starting with the CID.
			*/
			if (( getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGEQREQ_0_CID_VAL_MIN, TEL_AT_CGEQREQ_0_CID_VAL_MAX, TEL_AT_CGEQREQ_0_CID_VAL_DEFAULT) == TRUE )  &&
			    ( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
			{
				cmdValid = getAt3GQosParams(parameter_values_p, &qosProfile);

				if ( cmdValid == TRUE )
					ret = PS_Set3GQualityOfService(atHandle, cid - 1, &qosProfile, CI_PS_3G_QOSTYPE_MIN);
				else
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
			}       /*  if (( getExtValue(parameter_values_p, X, &cid */
			else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );

			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGEQREQ */
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#endif

#endif

/************************************************************************************
 * F@: ciPdpErrorReport - GLOBAL API for AT+PEER  -command
 *
 */
RETURNCODE_T  ciPdpErrorReport(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	RETURNCODE_T        rc = INITIAL_RETURN_CODE;
	CiReturnCode            ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	char *pPdpErrCauseBuf;

	*xid_p = atHandle;

	DBGMSG("ciPdpErrorReport: atHandle = %d, op=%d", atHandle, op);
#ifndef SINGLE_SIM
	if (!GET_SIM1_FLAG(atHandle))
		pPdpErrCauseBuf = gPdpErrCauseBuf;
	else
		pPdpErrCauseBuf = gPdpErrCauseBuf_1;
#else
	pPdpErrCauseBuf = gPdpErrCauseBuf;
#endif
	switch(op)
	{
		case TEL_EXT_ACTION_CMD:   /* AT+PEER */
		{
			char tmpBuf[300];
			sprintf(tmpBuf, "+PEER: \"%s\"\r\n", pPdpErrCauseBuf);
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, tmpBuf);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+PEER=? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+PEER= */
		case TEL_EXT_GET_CMD:       /* AT+PEER?  */
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
			break;
		}
	}

	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}
/************************************************************************************
 * F@: ciPdpErrorReport - GLOBAL API for AT*FastDorm  -command
 *
 */
RETURNCODE_T ciFastDormancy(            const utlAtParameterOp_T op,
										const char                      *command_name_p,
										const utlAtParameterValue_P2c parameter_values_p,
										const size_t num_parameters,
										const char                      *info_text_p,
										unsigned int                    *xid_p,
										void                            *arg_p)
{
	UNUSEDPARAM(command_name_p);
	UNUSEDPARAM(parameter_values_p);
	UNUSEDPARAM(num_parameters);
	UNUSEDPARAM(info_text_p);
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;

	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	switch(op)
	{
		case TEL_EXT_ACTION_CMD:	/* AT*FASTDORM */
		{
			ret = PS_FastDormancy(atHandle);
			break;
		}
		case TEL_EXT_TEST_CMD:		/* AT*FASTDORM=? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*FASTDORM:\r\nOK");
			break;
		}
		case TEL_EXT_GET_CMD:                   /* AT*FASTDORM?  */
		case TEL_EXT_SET_CMD:                   /* AT*FASTDORM=  */
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CMS_OPERATION_NOT_SUPPORTED, NULL );
			break;
		}
	}
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/* Added by Daniel for LTE PC AT command server 20120201, begin */

/************************************************************************************
 * F@: getAt4GQosParams - extracts the 4G Qos attributes from AT command line string
 *
 */
BOOL getAt4GQosParams ( const utlAtParameterValue_P2c   parameter_values_p, CiPs4GQosProfile *qosProfile, BOOL *onlyCid )
{

	INT32                           qci=0;
	INT32                           dlgbr = 0;
	INT32                           ulgbr = 0;
	INT32                           dlmbr = 0;
	INT32                           ulmbr = 0;

	BOOL                        ambrPresent = 0;
	INT32                           apnDLAmber = 0;
	INT32                           anpULAmber = 0;
	BOOL                            cmdValid = FALSE;

	/*
	 **  Get QCI...
	 */
	if (getExtValue(parameter_values_p, 1, (int *)&qci, TEL_AT_CGEQOS_QCI_MIN, TEL_AT_CGEQOS_QCI_MAX, TEL_AT_CGEQOS_QCI_DEFAULT) == TRUE)
	{
        if((qci >= TEL_AT_CGEQOS_QCI_MIN)&&(qci <= TEL_AT_CGEQOS_QCI_MAX))
		{
			/*
			 **  Get DL_GBR...
			 */
			cmdValid = getExtValue( parameter_values_p, 2, (int *)&dlgbr,
									TEL_AT_CGEQOS_MIN_DLGBR, TEL_AT_CGEQOS_MAX_DLGBR, TEL_AT_CGEQOS_DEFAULT_DLGBR);
			if (cmdValid == TRUE)
			{
				/*
				 **  Get UL_GBR...
				 */
				cmdValid = getExtValue( parameter_values_p, 3, (int *)&ulgbr,
										TEL_AT_CGEQOS_MIN_ULGBR, TEL_AT_CGEQOS_MAX_ULGBR, TEL_AT_CGEQOS_DEFAULT_ULGBR);

				if ( cmdValid == TRUE )
				{
					/*
					 **  Get DL_MBR...
					 */
					cmdValid = getExtValue( parameter_values_p, 4, (int *)&dlmbr,
											TEL_AT_CGEQOS_MIN_DLMBR, TEL_AT_CGEQOS_MAX_DLMBR, TEL_AT_CGEQOS_DEFAULT_DLMBR);

					if ( cmdValid == TRUE )
					{
						/*
						 **  Get UL_MBR...
						 */

						cmdValid = getExtValue( parameter_values_p, 5, (int *)&ulmbr,
												TEL_AT_CGEQOS_MIN_ULMBR, TEL_AT_CGEQOS_MAX_ULMBR, TEL_AT_CGEQOS_DEFAULT_ULMBR);
					}
				}
		}
	}
	if( cmdValid == TRUE )
	{
		if (parameter_values_p[2].is_default && parameter_values_p[3].is_default
		&& parameter_values_p[4].is_default && parameter_values_p[5].is_default )
		{
			qosProfile->gbrMbrPresent = FALSE;
		}
		else
		{
			qosProfile->gbrMbrPresent = TRUE;
		}
		/*
		 **  Set the CI request parameters.
		 */
		qosProfile->qci = qci;
		qosProfile->guaranteedDLRate = dlgbr;
		qosProfile->guaranteedULRate =  ulgbr;
		qosProfile->maxDLRate = dlmbr;
		qosProfile->maxULRate = ulmbr;

		qosProfile->ambrPresent = FALSE;
		qosProfile->apnDLAmbr = 0;
		qosProfile->apnULAmbr = 0;
		}

		if(parameter_values_p[1].is_default && parameter_values_p[2].is_default &&
		   parameter_values_p[3].is_default && parameter_values_p[4].is_default &&
		   parameter_values_p[5].is_default)
		{
			*onlyCid = TRUE;
			cmdValid = FALSE;
		}
	}  /* if ( _atParamToCiEnum( parameter_values_p, X, &trafficClass, */
    else
	{
		*onlyCid = TRUE;
	}

	return cmdValid;
}


/************************************************************************************
 * F@: ciSet4GQOS - GLOBAL API for GCF AT+CGEQOS  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciSet4GQOS(            const utlAtParameterOp_T        op,
									 const char                      *command_name_p,
									 const utlAtParameterValue_P2c   parameter_values_p,
									 const size_t                    num_parameters,
									 const char                      *info_text_p,
									 unsigned int                    *xid_p,
									 void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	BOOL                            cmdValid = FALSE;
	INT32                           cid;
	BOOL                            bOnlyCid = FALSE;

	CiPs4GQosProfile                qosProfile;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	memset(&qosProfile, 0, sizeof(qosProfile));

	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGEQOS?  */
		{
	        ret = PS_Get4GQualityOfServiceList(atHandle,0);//start from first CID,more query is processed in CI_PS_PRIM_GET_4G_QOS_CNF
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGEQOS=? */
		{
			ret = PS_Get4GCapsQos(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGEQOS= */
		{
			/*
			 **  Extract the arguments starting with the CID.
			 */
			if (( getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGEQREQ_0_CID_VAL_MIN, TEL_AT_CGEQREQ_0_CID_VAL_MAX, TEL_AT_CGEQREQ_0_CID_VAL_DEFAULT) == TRUE )  &&
				( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
			{

				cmdValid = getAt4GQosParams(parameter_values_p, &qosProfile,&bOnlyCid);

				if( cmdValid == TRUE )
					ret = PS_Set4GQualityOfService(atHandle, cid-1, &qosProfile);
				else if(bOnlyCid == TRUE)
				{
					ret = PS_Delete4GEPSContext (atHandle, cid-1);
				}
				else
				{
					ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
				}
			}  /*  if (( getExtValue(parameter_values_p, X, &cid */
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGEQOS */
		default:
		{
	            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}



/************************************************************************************
 * F@: ciCGSCONTRDP - GLOBAL API for GCF AT+CGSCONTRDP  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCGSCONTRDP(            const utlAtParameterOp_T        op,
									   const char                      *command_name_p,
									   const utlAtParameterValue_P2c   parameter_values_p,
									   const size_t                    num_parameters,
									   const char                      *info_text_p,
									   unsigned int                    *xid_p,
									   void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32                           cid=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGSCONTRDP?  */
		{
			char tmpBuf[1000];
			memset(tmpBuf, 0, sizeof(tmpBuf));
			sprintf((char *)tmpBuf, "+CGSCONTRDP: (<cid>), (<p_cid>), (<bearer_id>)\r\n");
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)tmpBuf);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGSCONTRDP=? */
		{
			ret = PS_ReadSecDynActPara(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGSCONTRDP= */
		{
            /*
                       **  Extract the arguments starting with the CID.
                       */
            if(parameter_values_p[0].is_default == TRUE)
            {
                ret = PS_ReadSecDynPara(atHandle,TEL_AT_CGEQREQ_0_CID_VAL_MAX);
            }
            else if((getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGEQREQ_0_CID_VAL_MIN, TEL_AT_CGEQREQ_0_CID_VAL_MAX, TEL_AT_CGEQREQ_0_CID_VAL_DEFAULT) == TRUE) &&
				( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
            {
                ret = PS_ReadSecDynPara(atHandle, cid-1);
            }
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGSCONTRDP */
		default:
		{
	        ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciCGTFTRDP - GLOBAL API for GCF AT+CGTFTRDP  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCGTFTRDP(            const utlAtParameterOp_T        op,
									 const char                      *command_name_p,
									 const utlAtParameterValue_P2c   parameter_values_p,
									 const size_t                    num_parameters,
									 const char                      *info_text_p,
									 unsigned int                    *xid_p,
									 void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32                           cid=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;


	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGTFTRDP?  */
		{
			char tmpBuf[1000];
			memset(tmpBuf, 0, sizeof(tmpBuf));
			sprintf((char *)tmpBuf, "+CGTFTRDP: (<cid>),(<operation code>), (<No of PKT filters>), (<packet filter identifier>), (<evaluation precedence index>), (<source address and subnet mask>), (<protocol number(ipv4)/next header(ipv6)>), (<destination port range>), (<source port range>), (<ipspec security parameter index(spi)>), (<type of service(tos)(ipv4) and mask/traffic class(ipv6) and mask>), (<flow label(ipv6)>), (<direction>)\r\n");
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)tmpBuf);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGTFTRDP=? */
		{
			ret = PS_ReadDynActTFT(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGTFTRDP= */
		{
            /*
                       **  Extract the arguments starting with the CID.
                       */
            if(parameter_values_p[0].is_default == TRUE)
            {
                ret = PS_ReadDynTFT(atHandle, TEL_AT_CGEQREQ_0_CID_VAL_MAX);
            }
            else if((getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGEQREQ_0_CID_VAL_MIN, TEL_AT_CGEQREQ_0_CID_VAL_MAX, TEL_AT_CGEQREQ_0_CID_VAL_DEFAULT) == TRUE) &&
				( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
			{
				ret = PS_ReadDynTFT(atHandle, cid-1);
			}  /*  if (( getExtValue(parameter_values_p, X, &cid */
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGTFTRDP */
		default:
		{
	            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}



/************************************************************************************
 * F@: ciCGEREP - GLOBAL API for GCF AT+CGEREP  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCGEREP(            const utlAtParameterOp_T        op,
								   const char                      *command_name_p,
								   const utlAtParameterValue_P2c   parameter_values_p,
								   const size_t                    num_parameters,
								   const char                      *info_text_p,
								   unsigned int                    *xid_p,
								   void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32                           mode=0;
	INT32							bfr=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;
	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGEREP?  */
		{
	            ret = PS_Get4GRep(atHandle);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGEREP =? */
		{
			ret = PS_Get4GCapsRep(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGEREP = */
		{
			/*
			 **  Extract the arguments starting with the CID.
			 */
			if ( getExtValue(parameter_values_p, 0, (int *)&mode, TEL_AT_CGEREP_MIN_MODE, TEL_AT_CGEREP_MAX_MODE, TEL_AT_CGEREP_DEFAULT_MODE) == TRUE )
			{
				if ( getExtValue(parameter_values_p, 1, (int *)&bfr, TEL_AT_CGEREP_MIN_BFR, TEL_AT_CGEREP_MAX_BFR, TEL_AT_CGEREP_DEFAULT_BFR) == TRUE )
				{
	                    ret = PS_Set4GRep(atHandle, mode, bfr);
				}
			}  /*  if (( getExtValue(parameter_values_p, X, &cid */
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGEREP */
		default:
		{
	            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciCVMOD - GLOBAL API for GCF AT+CVMOD  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCVMOD(            const utlAtParameterOp_T        op,
								  const char                      *command_name_p,
								  const utlAtParameterValue_P2c   parameter_values_p,
								  const size_t                    num_parameters,
								  const char                      *info_text_p,
								  unsigned int                    *xid_p,
								  void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32							cid=0;
	INT32                           mode=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;


	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CVMOD?  */
		{
			ret = PS_GetVoiceMode(atHandle, 0);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CVMOD =? */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, "+CVMOD:(0-3)\r\n");
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CVMOD = */
		{
			/*
			 **  Extract the arguments starting with the CID.
			 */
			if ( getExtValue(parameter_values_p, 0, (int *)&mode, TEL_AT_CVMODE_MIN_MODE, TEL_AT_CVMODE_MAX_MODE, TEL_AT_CVMODE_DEFAULT_MODE) == TRUE )
			{
				ret = PS_SetVoiceMode(atHandle, 0, mode);
			}  /*  if (( getExtValue(parameter_values_p, X, &cid */
			else
			{
				ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
			}
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CVMOD */
		default:
		{
	            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciCEMODE - GLOBAL API for GCF AT+CEMODE  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCEMODE( 		   const utlAtParameterOp_T 	   op,
							 const char						*command_name_p,
							 const utlAtParameterValue_P2c	parameter_values_p,
							 const size_t					num_parameters,
							 const char						*info_text_p,
							 unsigned int					*xid_p,
							 void							*arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32                           mode=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;


	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CEMODE?  */
		{
	            ret = PS_GetEpsMode(atHandle);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CEMODE =? */
		{
			ret = PS_GetCapsEpsMode(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CEMODE = */
		{
			/*
			 **  Extract the arguments starting with the CID.
			 */
            if(getExtValue(parameter_values_p, 0, (int *)&mode, TEL_AT_CEMODE_MIN_MODE, TEL_AT_CEMODE_MAX_MODE, TEL_AT_CEMODE_DEFAULT_MODE) == TRUE)
            {
                ret = PS_SetEpsMode(atHandle, mode);
            }
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CVMODE */
		default:
		{
	            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciCGPADDR - GLOBAL API for GCF AT+CGPADDR  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCGPADDR( 		   const utlAtParameterOp_T 	   op,
							  const char						*command_name_p,
							  const utlAtParameterValue_P2c	parameter_values_p,
							  const size_t					num_parameters,
							  const char						*info_text_p,
							  unsigned int					*xid_p,
							  void							*arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
    UINT32              cid[CI_PS_MAX_CID];
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	memset(cid, 0, sizeof(cid));


	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGPADDR?  */
		{
	        char tmpBuf[100];
			memset(tmpBuf, 0, sizeof(tmpBuf));
			sprintf((char *)tmpBuf, "+CGPADDR: (<cid>), (<PDP_addr>)\r\n");
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)tmpBuf);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGPADDR =? */
		{
			ret = PS_Get4GCapsAddr(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGPADDR = */
		{
			BOOL cmdValid = FALSE;
			UINT32 num = 0;
			int i;
			int temp = 0;

			if(parameter_values_p[0].is_default == TRUE)
			{
				for(i = 0; i < CI_PS_MAX_CID; i++)
				{
					cid[num++] = i;
				}
				ret = PS_Get4GAddr(atHandle, num, cid);
				break;
			}
			/*
			 **  Extract the arguments starting with the CID.
			 */
			for(i = 0; i < CI_PS_MAX_CID; i++)
			{
				if(parameter_values_p[i].is_default == TRUE)
					break;
				if(getExtValue(parameter_values_p, i, &temp, 1, CI_PS_MAX_CID, 1) == TRUE)
				{
					cmdValid = TRUE;
					cid[num++] = temp - 1;
				}
				else
				{
					cmdValid = FALSE;
					break;
				}
			}
			if(cmdValid)
			{
				ret = PS_Get4GAddr(atHandle, num, cid);
			}
			else
			{
				ret = ATRESP(atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
			}
			break;
		}

        case TEL_EXT_ACTION_CMD:   /* AT+CGPADDR */
        default:
        {
            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
            break;
        }
    }

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciCGPIAF - GLOBAL API for GCF AT+CGPIAF  -command
 *
 */
RETURNCODE_T  ciCGPIAF( 		   const utlAtParameterOp_T 	   op,
							  const char						*command_name_p,
							  const utlAtParameterValue_P2c	parameter_values_p,
							  const size_t					num_parameters,
							  const char						*info_text_p,
							  unsigned int					*xid_p,
							  void							*arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	int *pIPv6_AddressFormat, *pIPv6_SubnetNotation, *pIPv6_LeadingZeros, *pIPv6_CompressZeros;

	*xid_p = atHandle;

	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

#ifndef SINGLE_SIM
	if (!GET_SIM1_FLAG(atHandle)) {
		pIPv6_AddressFormat = &gIPv6_AddressFormat;
		pIPv6_SubnetNotation = &gIPv6_SubnetNotation;
		pIPv6_LeadingZeros = &gIPv6_LeadingZeros;
		pIPv6_CompressZeros = &gIPv6_CompressZeros;
	} else {
		pIPv6_AddressFormat = &gIPv6_AddressFormat_1;
		pIPv6_SubnetNotation = &gIPv6_SubnetNotation_1;
		pIPv6_LeadingZeros = &gIPv6_LeadingZeros_1;
		pIPv6_CompressZeros = &gIPv6_CompressZeros_1;
	}

#else
	pIPv6_AddressFormat = &gIPv6_AddressFormat;
	pIPv6_SubnetNotation = &gIPv6_SubnetNotation;
	pIPv6_LeadingZeros = &gIPv6_LeadingZeros;
	pIPv6_CompressZeros = &gIPv6_CompressZeros;

#endif
	/*mischecked by klocwork*/
	/*klocwork[Inconsistent Case Labels]*/
	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT+CGPIAF?  */
		{
			char tmpBuf[100];
			memset(tmpBuf, 0, sizeof(tmpBuf));
			sprintf(tmpBuf, "+CGPIAF: %d,%d,%d,%d\r\n", *pIPv6_AddressFormat, *pIPv6_SubnetNotation, *pIPv6_LeadingZeros, *pIPv6_CompressZeros);
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, tmpBuf);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT+CGPIAF =? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, "+CGPIAF: (0-1),(0-1),(0-1),(0-1)\r\n");
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT+CGPIAF = */
		{
			BOOL cmdValid = FALSE;
			if(getExtValue(parameter_values_p, 0, pIPv6_AddressFormat, TEL_AT_CGPIAF_IPV6ADDRFORMAT_VAL_MIN, TEL_AT_CGPIAF_IPV6ADDRFORMAT_VAL_MAX, TEL_AT_CGPIAF_IPV6ADDRFORMAT_VAL_DEFAULT) == TRUE)
			{
				if(getExtValue(parameter_values_p, 1, pIPv6_SubnetNotation, TEL_AT_CGPIAF_IPV6SUBNOTATION_VAL_MIN, TEL_AT_CGPIAF_IPV6SUBNOTATION_VAL_MAX, TEL_AT_CGPIAF_IPV6SUBNOTATION_VAL_DEFAULT) == TRUE )
				{
					if(getExtValue(parameter_values_p, 2, pIPv6_LeadingZeros, TEL_AT_CGPIAF_IPV6LEADZEROS_VAL_MIN, TEL_AT_CGPIAF_IPV6LEADZEROS_VAL_MAX, TEL_AT_CGPIAF_IPV6LEADZEROS_VAL_DEFAULT) == TRUE)
					{
						if(getExtValue(parameter_values_p, 3, pIPv6_CompressZeros, TEL_AT_CGPIAF_IPV6COMPZEROS_VAL_MIN, TEL_AT_CGPIAF_IPV6COMPZEROS_VAL_MAX, TEL_AT_CGPIAF_IPV6COMPZEROS_VAL_DEFAULT) == TRUE)
						{
							cmdValid = TRUE;
						}
					}
				}
			}
			if(cmdValid)
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, 0);
			else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGPIAF */
		default:
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

#ifdef VOLTE_ENABLE
/************************************************************************************
 * F@: ciIRegStatus - GLOBAL API for GCF AT+CIREG  -command
 *
 */
RETURNCODE_T  ciIRegStatus(const utlAtParameterOp_T op,
    const char                      *command_name_p,
    const utlAtParameterValue_P2c   parameter_values_p,
    const size_t                    num_parameters,
    const char                      *info_text_p,
    unsigned int                    *xid_p,
    void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T    rc = INITIAL_RETURN_CODE;
	CiReturnCode    ret = CIRC_FAIL;
	UINT32          atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);

	*xid_p = atHandle;
	//DBGMSG(ciIRegStatus, "ciIRegStatus: atHandle = %d.\n", atHandle);

	/*
	 **  Check the operation type.
	 */
	 /*mischecked by klocwork*/
	/*klocwork[Inconsistent Case Labels]*/
	switch( op )
	{
		case TEL_EXT_GET_CMD:     /* AT+CIREG? */
		{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, "+CIREG: 1, 0");
			break;
		}

		case TEL_EXT_SET_CMD:     /* AT+CIREG= */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			break;
		}

		default:
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#endif

/************************************************************************************
 * F@: ciPsPowerOnAutoAttach - GLOBAL API for AT*CGATT -command
 *
 */
RETURNCODE_T  ciPsPowerOnAutoAttach(    const utlAtParameterOp_T op,
				     const char                      *command_name_p,
				     const utlAtParameterValue_P2c parameter_values_p,
				     const size_t num_parameters,
				     const char                      *info_text_p,
				     unsigned int                    *xid_p,
				     void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	UINT32 enable=0;

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	*xid_p = atHandle;

	switch ( op )
	{
		case TEL_EXT_SET_CMD:
			if (getExtValue(parameter_values_p, 0, (int *)&enable, 0, 1, 0) == TRUE )
			{
				ret = PS_SetPsPowerOnAutoAttach(atHandle, (BOOL)enable);
			}
			else
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			break;

		case TEL_EXT_GET_CMD:
			ret = PS_GetPsPowerOnAutoAttach(atHandle);
			break;

		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
	}
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

#if (!defined(LTEONLY_THIN) && !defined(LTEGSM_THIN)) 

/************************************************************************************
 * F@: ciCGCLASS - GLOBAL API for GCF AT+CGCLASS  -command
 *
 * NOTE: This function does not approximate error ratio value: if a value not defined in 24.008, it is rejected
 */
RETURNCODE_T  ciCGCLASS(const utlAtParameterOp_T op,
    const char                        *command_name_p,
    const utlAtParameterValue_P2c    parameter_values_p,
    const size_t                    num_parameters,
    const char                        *info_text_p,
    unsigned int                    *xid_p,
    void                            *arg_p)
{
    RETURNCODE_T        rc = INITIAL_RETURN_CODE;
    CiReturnCode        ret = CIRC_FAIL;
    char                classString[2] = {""};
    UINT16              classStringLength = 0;

    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID*)arg_p);

    *xid_p = atHandle;

    //DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

    switch(op)
    {
        case TEL_EXT_TEST_CMD:    /* AT+CGCLASS=?  */
        {
            ret = PS_GetGsmGprsClasses(atHandle);
            break;
        }

        case TEL_EXT_GET_CMD:    /* AT+CGCLASS? */
        {
            ret = PS_GetGsmGprsClass(atHandle);
            break;
        }

        case TEL_EXT_SET_CMD:      /* AT+CGCLASS = */
        {
            /*
                       **  Extract the arguments starting with the CID.
                       */
			if(getExtString(parameter_values_p, 0, (CHAR *)classString, TEL_AT_CGCLASS_MAX_CLASS_LEN, (INT16 *)&classStringLength , (CHAR *)TEL_AT_CGCLASS_DEFAULT_CLASS) == TRUE)
            {
                ret = PS_SetGsmGprsClass(atHandle, classString, classStringLength);
            }
            break;
        }

        case TEL_EXT_ACTION_CMD:   /* AT+CGCLASS */
        default:
        {
            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
            break;
        }
    }


    /* handle the return value */
    rc = HANDLE_RETURN_VALUE(ret);
    return(rc);
}


#ifdef OPERATOR_VERIZON

/************************************************************************************
 * F@: ciVZWAPNE - GLOBAL API for AT+VZWAPNE -command
 *
 */
RETURNCODE_T  ciVZWAPNE(    const utlAtParameterOp_T op,
				     const char                      *command_name_p,
				     const utlAtParameterValue_P2c parameter_values_p,
				     const size_t num_parameters,
				     const char                      *info_text_p,
				     unsigned int                    *xid_p,
				     void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	INT32 tmpParam = 0;
	CHAR tmpStr[TEL_AT_VZWAPNE_2_APN_STR_MAX_LEN + ATCI_NULL_TERMINATOR_LENGTH] = "";
	INT16 tmpStrLen = 0;
	CiPsPrimSetApnReq setApnReq;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	*xid_p = atHandle;

	memset(&setApnReq, 0, sizeof(setApnReq));

	switch ( op )
	{
		case TEL_EXT_SET_CMD:
		{
			if (getExtValue(parameter_values_p, 0, (int *)&tmpParam, TEL_AT_VZWAPNE_0_WAPN_VAL_MIN, TEL_AT_VZWAPNE_0_WAPN_VAL_MAX, TEL_AT_VZWAPNE_0_WAPN_VAL_DEFAULT) == TRUE )
			{
				if(tmpParam == 0)
				{
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
					break;
				}
				else
					setApnReq.wapn = tmpParam - 1;
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			if (getExtValue(parameter_values_p, 1, (int *)&tmpParam, TEL_AT_VZWAPNE_1_APNCL_VAL_MIN, TEL_AT_VZWAPNE_1_APNCL_VAL_MAX, TEL_AT_VZWAPNE_1_APNCL_VAL_DEFAULT) == TRUE )
			{
				setApnReq.apncl = tmpParam;
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			if ( getExtString(parameter_values_p, 2, tmpStr, TEL_AT_VZWAPNE_2_APN_STR_MAX_LEN, &tmpStrLen, TEL_AT_VZWAPNE_2_APN_STR_DEFAULT) == TRUE )
			{
				setApnReq.apnni.len = tmpStrLen;
				memcpy(setApnReq.apnni.valStr, tmpStr, tmpStrLen);
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			if ( getExtString(parameter_values_p, 3, tmpStr, TEL_AT_VZWAPNE_3_APNTYPE_STR_MAX_LEN, &tmpStrLen, (CHAR *)TEL_AT_VZWAPNE_3_APNTYPE_STR_DEFAULT) == TRUE )
			{
				setApnReq.apnTypePresent = TRUE;
				if (strcasecmp("IP", (char *)tmpStr) == 0)
					setApnReq.apnType = CI_PS_APN_ADDR_IPV4_TYPE;
				else if (strcasecmp("IPv6", (char *)tmpStr) == 0)
					setApnReq.apnType = CI_PS_APN_ADDR_IPV6_TYPE;
				else if (strcasecmp("IPv4v6", (char *)tmpStr) == 0)
					setApnReq.apnType = CI_PS_APN_ADDR_IPV4V6_TYPE;
				else
				{
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
					break;
				}
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			if (getExtString(parameter_values_p, 4, tmpStr, TEL_AT_VZWAPNE_4_APNBEAR_STR_MAX_LEN, &tmpStrLen, (CHAR *)TEL_AT_VZWAPNE_4_APNBEAR_STR_DEFAULT) == TRUE )
			{
				setApnReq.apnBearPresent = TRUE;
				if (strcasecmp("LTE", (char *)tmpStr) == 0)
					setApnReq.apnBear = CI_PS_APN_BEAR_LTE_TYPE;
				else
				{
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
					break;
				}
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			if (getExtString(parameter_values_p, 5, tmpStr, TEL_AT_VZWAPNE_5_APNED_STR_MAX_LEN, &tmpStrLen, (CHAR *)TEL_AT_VZWAPNE_5_APNED_STR_DEFAULT) == TRUE )
			{
				setApnReq.apnedPresent = TRUE;
				if (strcasecmp("Enabled", (char *)tmpStr) == 0)
					setApnReq.apned = 1;
				else if (strcasecmp("Disabled", (char *)tmpStr) == 0)
					setApnReq.apned = 0;
				else
				{
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
					break;
				}
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			if (getExtValue(parameter_values_p, 6, (int *)&tmpParam, TEL_AT_VZWAPNE_6_APNTIME_VAL_MIN, TEL_AT_VZWAPNE_6_APNTIME_VAL_MAX, TEL_AT_VZWAPNE_6_APNTIME_VAL_DEFAULT) == TRUE )
			{
				setApnReq.apnTimePresent = TRUE;
				setApnReq.apnTime = tmpParam;
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				break;
			}

			ret = PS_SetAPNList(atHandle, &setApnReq);
			break;
		}

		case TEL_EXT_GET_CMD:
			ret = PS_GetAPNList(atHandle);
			break;

		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
	}
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
/* Added by Daniel for LTE PC AT command server 20120201, end */
#endif
#endif

#ifndef LTEONLY_THIN

/************************************************************************************
 * F@: ciPdpErrorReport - GLOBAL API for AT*FDY  -command
 *
 */
RETURNCODE_T ciStarFDY(            const utlAtParameterOp_T op,
					    const char                      *command_name_p,
					   const utlAtParameterValue_P2c parameter_values_p,
					   const size_t num_parameters,
					   const char                      *info_text_p,
					   unsigned int                    *xid_p,
					   void                            *arg_p)
{
	UNUSEDPARAM(command_name_p);
	UNUSEDPARAM(parameter_values_p);
	UNUSEDPARAM(num_parameters);
	UNUSEDPARAM(info_text_p);
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;

	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	switch(op)
	{
		case TEL_EXT_SET_CMD:	/* AT*FDY= */
		{
			BOOL cmdValid = FALSE;
			int mode;
			int lcdOnTimerInterval, lcdOffTimerInterval, rel8LcdOnTimerInterval, rel8LcdOffTimerInterval;
			if (getExtValue(parameter_values_p, 0, &mode, TEL_AT_FDY_0_TYPE_VAL_MIN, TEL_AT_FDY_0_TYPE_VAL_MAX, TEL_AT_FDY_0_TYPE_VAL_DEFAULT) == TRUE )
			{
				if (getExtValue(parameter_values_p, 1, &lcdOnTimerInterval, TEL_AT_FDY_1_INTERVAL_VAL_MIN, TEL_AT_FDY_1_INTERVAL_VAL_MAX, TEL_AT_FDY_1_INTERVAL_VAL_DEFAULT) == TRUE )
				{
					if (getExtValue(parameter_values_p, 2, &lcdOffTimerInterval, TEL_AT_FDY_2_INTERVAL_VAL_MIN, TEL_AT_FDY_2_INTERVAL_VAL_MAX, TEL_AT_FDY_2_INTERVAL_VAL_DEFAULT) == TRUE )
					{
						if (getExtValue(parameter_values_p, 3, &rel8LcdOnTimerInterval, TEL_AT_FDY_3_INTERVAL_VAL_MIN, TEL_AT_FDY_3_INTERVAL_VAL_MAX, TEL_AT_FDY_3_INTERVAL_VAL_DEFAULT) == TRUE )
						{
							if (getExtValue(parameter_values_p, 4, &rel8LcdOffTimerInterval, TEL_AT_FDY_4_INTERVAL_VAL_MIN, TEL_AT_FDY_4_INTERVAL_VAL_MAX, TEL_AT_FDY_4_INTERVAL_VAL_DEFAULT) == TRUE )
							{
    			    			FdyMode = mode;
    			    			FdyLcdOnTimer = lcdOnTimerInterval;
    			    			FdyLcdOffTimer = lcdOffTimerInterval;
    			    			FdyRel8LcdOnTimer = rel8LcdOnTimerInterval;
    			    			FdyRel8LcdOffTimer = rel8LcdOffTimerInterval;
								cmdValid = TRUE;
							}
						}
					}
				}
			}
			if(cmdValid)
			{
				ret = PS_SetFDY(atHandle, (UINT8)mode, (UINT32)lcdOnTimerInterval, (UINT32)lcdOffTimerInterval, (UINT32)rel8LcdOnTimerInterval, (UINT32)rel8LcdOffTimerInterval);
			}
			else
			{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
			}
			break;
		}
        case TEL_EXT_GET_CMD:       /* AT*FDY?  */
        {
            char repBuf[30]={0};

            sprintf(repBuf, "*FDY: %d,%d,%d,%d,%d\r\n", FdyMode, FdyLcdOnTimer, FdyLcdOffTimer, FdyRel8LcdOnTimer, FdyRel8LcdOffTimer);
            ret = ATRESP( atHandle,ATCI_RESULT_CODE_OK,0,(char *)repBuf);
            break;
        }


		case TEL_EXT_TEST_CMD:		/* AT*FDY=? */
		case TEL_EXT_ACTION_CMD:    /* AT*FDY  */
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
			break;
		}
	}
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}
#endif

/************************************************************************************
 * F@: ciMobileData - GLOBAL API for AT*PSPG -command
 *
 */
RETURNCODE_T  ciPsPlusPaging(    const utlAtParameterOp_T op,
				     const char                      *command_name_p,
				     const utlAtParameterValue_P2c parameter_values_p,
				     const size_t num_parameters,
				     const char                      *info_text_p,
				     unsigned int                    *xid_p,
				     void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	UINT32 enable;

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	*xid_p = atHandle;

	switch ( op )
	{
		case TEL_EXT_SET_CMD:
			if (getExtValue(parameter_values_p, 0, (int *)&enable, 0, 1, 0) == TRUE )
			{
				ret = PS_SetPsPlusPaging(atHandle, (BOOL)enable);
			}
			else
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			break;

		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
	}
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

#ifdef VOLTE_ENABLE


/************************************************************************************
 * F@: ciSetVoiceDomainPreference - GLOBAL API for AT+CEVDP -command
 *
 */
RETURNCODE_T  ciEutranVoiceDomainPreference(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Check the operation type.
	*/
	switch ( op )
	{
		case TEL_EXT_GET_CMD:              /* AT+CEVDP? */
		{
			ret = PS_GetVoiceDomainPreference(atHandle, TRUE);
			break;
		}

		case TEL_EXT_SET_CMD:              /* AT+CEVDP= */
		{
			int setting;
			if ( getExtValue( parameter_values_p, 0, &setting, TEL_AT_VOICE_DOMAIN_PREFERENCE_MIN, TEL_AT_EUTRAN_VOICE_DOMAIN_PREFERENCE_MAX, TEL_AT_VOICE_DOMAIN_PREFERENCE_DEFAULT ) == TRUE )
			{
					ret = PS_SetVoiceDomainPreference(atHandle, (UINT8)setting, TRUE);
			}
			else
			{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			}

			break;
		}

		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#endif
/************************************************************************************
 * F@: ciEpsUsageSetting - GLOBAL API for AT+CEUS -command
 *
 */
RETURNCODE_T  ciEpsUsageSetting(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Check the operation type.
	*/
	switch ( op )
	{
		case TEL_EXT_GET_CMD:              /* AT+CEUS? */
		{
			ret = PS_GetEpsUsageSetting(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:              /* AT+CEUS= */
		{
			int setting;
			if ( getExtValue( parameter_values_p, 0, &setting, TEL_AT_EPS_USAGE_SETTING_MIN, TEL_AT_EPS_USAGE_SETTING_MAX, TEL_AT_EPS_USAGE_SETTING_DEFAULT ) == TRUE )
			{
					ret = PS_SetEpsUsageSetting(atHandle, (UINT8)setting);
			}
			else
			{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			}

			break;
		}

		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciSetAcl - GLOBAL API for AT*PSDC -command
 *
 */
RETURNCODE_T  ciSePsdc(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	CiReturnCode                        ret = CIRC_FAIL;
	RETURNCODE_T                        rc = INITIAL_RETURN_CODE;
	INT32                               simaclpresent=0;
	INT32                               simaclEnable=0;
	INT32                               psaclpresent=0;
	INT32                               psaclEnable=0;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	BOOL                         cmdValid = FALSE;

	*xid_p = atHandle;

	switch (op)
	{

		case TEL_EXT_SET_CMD:  /* AT*PSDC=   */
		{
			INT32  ps_enable=0;
			if ( getExtValue( parameter_values_p, 0, (int *)&ps_enable, 0, 1, 1 ) == TRUE )
			{
				ret = PS_SetPsdc_req(atHandle, ps_enable);
			}
			else
			{
				ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
			}

		break;
	    }

		case TEL_EXT_GET_CMD:	 /* AT*PSDC?  */
		{

			ret = PS_GetPsdc_req(atHandle);
		}
		case TEL_EXT_ACTION_CMD:   /* AT*PSDC   */
		case TEL_EXT_TEST_CMD:	  /* AT*PSDC=? */
		default:
			{
				break;
			}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

#ifdef LWIP_IPNETBUF_SUPPORT
#ifdef VOLTE_ENABLE  
extern UINT8 gMmiIndSim;
extern BOOL gImsRegState;
extern BOOL gImsRegState_1;

extern void sendMmiCheckRsp(CiIndicationHandle respHandle);
/************************************************************************************
 * F@: ciNotifyImsRegState - GLOBAL API for AT*CIIND -command
 *
 */
RETURNCODE_T  ciNotifyImsRegState(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	int state;
	BOOL *pImsRegState;
	UINT32 respHandle = 0;

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

#ifndef SINGLE_SIM		
	if (!GET_SIM1_FLAG(atHandle))
		pImsRegState = &gImsRegState;
	else
		pImsRegState = &gImsRegState_1;
#else
	pImsRegState = &gImsRegState;
#endif

	/*
	**  Check the operation type.
	*/
	switch ( op )
	{
	case TEL_EXT_SET_CMD:              /* AT*CIIND= */
	{
		if ( getExtValue( parameter_values_p, 0, &state, TEL_AT_IMS_REG_STATE_MIN, TEL_AT_IMS_REG_STATE_MAX, TEL_AT_IMS_REG_STATE_DEFAULT) == TRUE )
		{
			if (state <= 128)
			{
				if(state == 5)   //reg_on
				{
					*pImsRegState = TRUE;
					CPUartLogPrintf("CIIND: IMS Reg status is on");
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		

				}
				else if(state == 6)   //reg_off
				{
					*pImsRegState = FALSE;
					CPUartLogPrintf("CIIND: IMS Reg status is off");
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);

				}
				else if(state == 7)   //deg_complete
				{
						CPUartLogPrintf("CIIND: finish dereg, send CI_SIM_PRIM_CHECK_MMI_STATE_RSP");

						if(gMmiIndSim == 0)
							respHandle = IND_REQ_HANDLE;
						else
							respHandle = IND_REQ_HANDLE_1;
	
						sendMmiCheckRsp(respHandle);
					
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);					
				}
				else
					ret = PS_NotifyImsRegStateToCp(atHandle, (UINT8)state);
			}
			else {
				switch(state) {
					case 129:
						g_imsSrvStatus = 1;
						CPUartLogPrintf("set IMS Srv status to 1");
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
						break;
					case 130:
						g_imsSrvStatus = 0;
						CPUartLogPrintf("set IMS Srv status to 0");
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);				
						break;	
					default:
						ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				}
			}
		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		}

		break;
	}

	default:
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciImsVoiceCallAvailability - GLOBAL API for AT+CAVIMS -command
 *
 */
RETURNCODE_T  ciImsVoiceCallAvailability(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Check the operation type.
	*/
	switch ( op )
	{
	case TEL_EXT_GET_CMD:              /* AT+CAVIMS? */
	{
		ret = PS_GetImsVoiceCallAvailability(atHandle);
		break;
	}

	case TEL_EXT_SET_CMD:              /* AT+CAVIMS= */
	{
		int state;
		if ( getExtValue( parameter_values_p, 0, &state, TEL_AT_IMS_VOICE_CALL_AVAILABILITY_MIN, TEL_AT_IMS_VOICE_CALL_AVAILABILITY_MAX, TEL_AT_IMS_VOICE_CALL_AVAILABILITY_DEFAULT ) == TRUE )
		{
			ret = PS_SetImsVoiceCallAvailability(atHandle, (UINT8)state);
		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		}

		break;
	}

	default:
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciImsSmsAvailability - GLOBAL API for AT+CASIMS -command
 *
 */
RETURNCODE_T  ciImsSmsAvailability(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Check the operation type.
	*/
	switch ( op )
	{
	case TEL_EXT_GET_CMD:              /* AT+CASIMS? */
	{
		ret = PS_GetImsSmsAvailability(atHandle);
		break;
	}

	case TEL_EXT_SET_CMD:              /* AT+CASIMS= */
	{
		int state;
		if ( getExtValue( parameter_values_p, 0, &state, TEL_AT_IMS_SMS_AVAILABILITY_MIN, TEL_AT_IMS_SMS_AVAILABILITY_MAX, TEL_AT_IMS_SMS_AVAILABILITY_DEFAULT ) == TRUE )
		{
			ret = PS_SetImsSmsAvailability(atHandle, (UINT8)state);
		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		}

		break;
	}

	default:
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/******************************************************************************
 ******
 * F@: ciPsSetImsSrv - GLOBAL API for GCF AT*IMSSRV command
 *
 */

RETURNCODE_T  ciPsSetImsSrv(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */
	switch ( op )
	{
	case TEL_EXT_SET_CMD:         /* AT*IMSSRV= */
	{
		int imsSrvType, imsSrvStatus, SrvFailCause;
		if ( getExtValue( parameter_values_p, 0, &imsSrvType, TEL_AT_IMS_SERVICE_TYPE_MIN, TEL_AT_IMS_SERVICE_TYPE_MAX, TEL_AT_IMS_SERVICE_TYPE_DEFAULT) == TRUE )
		{
			if(getExtValue( parameter_values_p, 1, &imsSrvStatus, TEL_AT_IMS_SERVICE_STATUS_MIN, TEL_AT_IMS_SERVICE_STATUS_MAX, TEL_AT_IMS_SERVICE_STATUS_DEFAULT) == TRUE )
			{
				if(getExtValue( parameter_values_p, 2, &SrvFailCause, TEL_AT_IMS_SERVICE_FAIL_CAUSE_MIN, TEL_AT_IMS_SERVICE_FAIL_CAUSE_MAX, TEL_AT_IMS_SERVICE_FAIL_CAUSE_DEFAULT) == TRUE )
				{
					ret = PS_SetImsSrv(atHandle, (UINT8)imsSrvType, (UINT8)imsSrvStatus,  (UINT8)SrvFailCause);
				}
				else
				{
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
				}
			}
			else
			{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
			}
		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
		}
		break;
	}

	default:         /* AT*IMSSRV */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/******************************************************************************
 ******
 * F@: ciPocCmd - GLOBAL API for  AT*POCCMD command
 *
 */

RETURNCODE_T  ciPocCmd(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */
	switch ( op )
	{
	case TEL_EXT_TEST_CMD: /*AT*POCCMD=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"AT*POCCMD: (0-1),(0-2)" );
		break;
	}


	case TEL_EXT_SET_CMD:         /* AT*POCCMD= */
	{
		int type;
        int status;
		if ( getExtValue( parameter_values_p, 0, &type, TEL_AT_POCCMD_TYPE_MIN, TEL_AT_POCCMD_TYPE_MAX, TEL_AT_POCCMD_TYPE_DEFAULT) == TRUE )
		{
			if(getExtValue( parameter_values_p, 1, &status, TEL_AT_POCCMD_STATUS_MIN, TEL_AT_POCCMD_STATUS_MAX, TEL_AT_POCCMD_STATUS_DEFAULT) == TRUE )
			{
                ret = PS_SetImsSrv(atHandle, (UINT8)(type+7) , (UINT8)status,  0);
			}
			else
			{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CME_INVALID_PARAM, NULL);
			}

		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CME_INVALID_PARAM, NULL);
		}
		break;
	}

	default:         /* AT*POCCMD */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CMS_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#endif
#endif

#ifdef VOLTE_ENABLE

/************************************************************************************
 * F@: ciMmImsVoiceTermination - GLOBAL API for AT+CMMIVT -command
 *
 */
RETURNCODE_T  ciMmImsVoiceTermination(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Check the operation type.
	*/
	switch ( op )
	{
	case TEL_EXT_GET_CMD:              /* AT+CMMIVT? */
	{
		ret = PS_GetMmImsVoiceTermination(atHandle);
		break;
	}

	case TEL_EXT_SET_CMD:              /* AT+CMMIVT= */
	{
		int setting;
		if ( getExtValue( parameter_values_p, 0, &setting, TEL_AT_MM_IMS_VOICE_TERMINATION_MIN, TEL_AT_MM_IMS_VOICE_TERMINATION_MAX, TEL_AT_MM_IMS_VOICE_TERMINATION_DEFAULT ) == TRUE )
		{
			ret = PS_SetMmImsVoiceTermination(atHandle, (UINT8)setting);
		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		}

		break;
	}

	default:
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


#endif


/******************************************************************************
 ******
 * F@: ciPsmConfig - GLOBAL API for GCF AT+CPSMS command
 *
 */

RETURNCODE_T  ciPsmConfig(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	INT16    rau_len,gprs_ready_timer_len,tau_len,active_timer_len;
    int     mode = 0;
	CHAR rau[TEL_AT_CPSMS_PARAMETER_STR_LENGTH+1], gprs_ready_timer[TEL_AT_CPSMS_PARAMETER_STR_LENGTH+1], tau[TEL_AT_CPSMS_PARAMETER_STR_LENGTH+1], active_timer[TEL_AT_CPSMS_PARAMETER_STR_LENGTH+1];
	UINT32 tempValue;
	CiPsPrimSetPsmConfigReq setPsmConfigReq;
	BOOL cmdValid = FALSE;

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	memset(&setPsmConfigReq, 0, sizeof(setPsmConfigReq));

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CPSMS=? */
		{
			/* It looks like CI request for this is not even defined */
			/* Print out hard-coded result */
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CPSMS: (0-2))" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CPSMS? */
		{
			ret = PS_GetPsmConfig(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CPSMS= */
		{

			if ( getExtValue( parameter_values_p, 0, &mode, TEL_AT_CPSMS_0_MODE_VAL_MIN, TEL_AT_CPSMS_0_MODE_VAL_MAX, TEL_AT_CPSMS_0_MODE_VAL_DEFAULT ) == TRUE )
			{
				setPsmConfigReq.mode = mode;
				if ( getExtString( parameter_values_p, 1, rau, TEL_AT_CPSMS_PARAMETER_STR_LENGTH, &rau_len, NULL ) == TRUE )
				{
					if(parameter_values_p[1].is_default == TRUE)
					{
						setPsmConfigReq.requestedPeriodRauPresent= FALSE;
					}
					else
					{
						setPsmConfigReq.requestedPeriodRauPresent= TRUE;
						if(convertBinStrToDecInt((UINT8 *)rau,&tempValue)==TRUE)
						{
							setPsmConfigReq.requestedPeriodRau = (UINT8)tempValue;
						}
						else
						{
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
							break;
						}
					}

					if ( getExtString( parameter_values_p, 2, gprs_ready_timer, TEL_AT_CPSMS_PARAMETER_STR_LENGTH, &gprs_ready_timer_len, NULL ) == TRUE )
					{
						if(parameter_values_p[2].is_default == TRUE)
						{
							setPsmConfigReq.requestedGprsReadyTimerPresent= FALSE;
						}
						else
						{
							setPsmConfigReq.requestedGprsReadyTimerPresent= TRUE;

							if(convertBinStrToDecInt((UINT8 *)gprs_ready_timer,&tempValue)==TRUE)
							{
								setPsmConfigReq.requestedGprsReadyTimer= (UINT8)tempValue;
							}
							else
							{
								ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
								break;
							}
						}


						if ( getExtString( parameter_values_p, 3,tau, TEL_AT_CPSMS_PARAMETER_STR_LENGTH, &tau_len, NULL ) == TRUE )
						{
							if(parameter_values_p[3].is_default == TRUE)
							{
								setPsmConfigReq.requestedPeriodicTauPresent= FALSE;
							}
							else
							{
								setPsmConfigReq.requestedPeriodicTauPresent= TRUE;
								if(convertBinStrToDecInt((UINT8 *)tau,&tempValue)==TRUE)
								{
									setPsmConfigReq.requestedPeriodicTau= (UINT8)tempValue;
								}
								else
								{
									ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
									break;
								}
							}



							if ( getExtString( parameter_values_p, 4,active_timer, TEL_AT_CPSMS_PARAMETER_STR_LENGTH, &active_timer_len, NULL ) == TRUE )
							{
								if(parameter_values_p[4].is_default == TRUE)
								{
									setPsmConfigReq.requestedActiveTimePresent= FALSE;
								}
								else
								{
									setPsmConfigReq.requestedActiveTimePresent= TRUE;
									if(convertBinStrToDecInt((UINT8 *)active_timer,&tempValue)==TRUE)
									{
										setPsmConfigReq.requestedActiveTime= (UINT8)tempValue;
									}
									else
									{
										ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
										break;
									}
								}
								cmdValid = TRUE;
								ret = PS_SetPsmConfig(atHandle,  &setPsmConfigReq);
							}
						}

					}

				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		default: /* AT+CPSMS */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/******************************************************************************
 ******
 * F@: ciCEDRXS - GLOBAL API for GCF AT+CEDRXS
 *
 */
RETURNCODE_T  ciCEDRXS(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL cmdValid = FALSE;
	int mode,actType;
	UINT32 tempVal;
	INT16 eDrxLen;
	CHAR eDrx[TEL_AT_CEDRXS_EDRX_STR_LENGTH+1];
	CiPsPrimSetEdrxConfigReq setEdrxConfigReq;
	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);


	memset(&setEdrxConfigReq, 0, sizeof(setEdrxConfigReq));

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CEDRXS=? */
		{
			/* It looks like CI request for this is not even defined */
			/* Print out hard-coded result */
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CEDRXS: (0-3),(1-5)" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CEDRXS? */
		{
			ret = PS_GetEdrxConfig(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CEDRXS= */
		{

			if ( getExtValue( parameter_values_p, 0, &mode, TEL_AT_CEDRXS_0_MODE_VAL_MIN, TEL_AT_CEDRXS_0_MODE_VAL_MAX, TEL_AT_CEDRXS_0_MODE_VAL_DEFAULT ) == TRUE )
			{
				setEdrxConfigReq.mode = mode;
				if ( getExtValue( parameter_values_p, 1,&actType, TEL_AT_CEDRXS_1_ACT_TYPE_VAL_MIN, TEL_AT_CEDRXS_1_ACT_TYPE_VAL_MAX, TEL_AT_CEDRXS_1_ACT_TYPE_VAL_DEFAULT ) == TRUE )
				{	

                    if(!mode)
                        setEdrxConfigReq.eDrxAct = CIPS_EDRX_ACT_EUTRAN;  //set to 4 when mode is 0
                    else
					    setEdrxConfigReq.eDrxAct = actType;

					if ( getExtString( parameter_values_p, 2,eDrx, TEL_AT_CEDRXS_EDRX_STR_LENGTH, &eDrxLen, NULL ) == TRUE )
					{
						if(parameter_values_p[2].is_default == TRUE)
						{
							setEdrxConfigReq.requestedEdrxValuePresent= FALSE;
						}
						else
						{
							setEdrxConfigReq.requestedEdrxValuePresent= TRUE;

							if(eDrxLen!=0)
								setEdrxConfigReq.requestedEdrxValue= atoi(eDrx);
						}
						cmdValid = TRUE;
						ret = PS_SetEdrxConfig(atHandle,&setEdrxConfigReq);
					}
				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		default: /* AT+CEDRXS */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/************************************************************************************
 * F@: ciCEDRXRDP - GLOBAL API for AT+CEDRXRDP	-command
 *
 */
RETURNCODE_T  ciCEDRXRDP(			 const utlAtParameterOp_T op,
					  const char					  *command_name_p,
					  const utlAtParameterValue_P2c parameter_values_p,
					  const size_t num_parameters,
					  const char					  *info_text_p,
					  unsigned int					  *xid_p,
					  void							  *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**	Check the operation type.
	*/
	/*mischecked by klocwork*/
	/*klocwork[Inconsistent Case Labels]*/
	switch ( op )
	{
	    case TEL_EXT_ACTION_CMD:  /* AT+CEDRXRDP*/
		{
			ret = PS_ReadEdrxDynParaReq(atHandle);
			break;
		}
		case TEL_EXT_GET_CMD:			   /* AT+CEDRXRDP? */
		case TEL_EXT_SET_CMD:			   /* AT+CEDRXRDP= */
		case TEL_EXT_TEST_CMD:				/* AT+CEDRXRDP=? */
		default:
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/******************************************************************************
 ******
 * F@: ciCCIOTOPT - GLOBAL API for GCF AT+CCIOTOPT
 *
 */
RETURNCODE_T  ciCCIOTOPT(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL cmdValid = FALSE;
	int mode,supportedUEOpt,preferredUEOpt;

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CCIOTOPT=? */
		{
			/* It looks like CI request for this is not even defined */
			/* Print out hard-coded result */
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CCIOTOPT: (0-2),(0-3),(0-2)" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CCIOTOPT? */
		{
			ret = PS_GetCiotConfigReq(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CCIOTOPT= */
		{

			if ( getExtValue( parameter_values_p, 0, &mode, TEL_AT_CCIOTOPT_0_MODE_VAL_MIN, TEL_AT_CCIOTOPT_0_MODE_VAL_MAX, TEL_AT_CCIOTOPT_0_MODE_VAL_DEFAULT) == TRUE )
			{
				if ( getExtValue( parameter_values_p, 1,&supportedUEOpt, TEL_AT_CCIOTOPT_1_SUPPORTED_UE_OPT_VAL_MIN, TEL_AT_CCIOTOPT_1_SUPPORTED_UE_OPT_VAL_MAX, TEL_AT_CCIOTOPT_1_SUPPORTED_UE_OPT_VAL_DEFAULT) == TRUE )
				{
					if ( getExtValue( parameter_values_p, 2,&preferredUEOpt, TEL_AT_CCIOTOPT_2_PREFERRED_UE_OPT_VAL_MIN, TEL_AT_CCIOTOPT_2_PREFERRED_UE_OPT_VAL_MAX, TEL_AT_CCIOTOPT_2_PREFERRED_UE_OPT_VAL_DEFAULT) == TRUE )
					{
						cmdValid = TRUE;
						ret = PS_SetCiotConfigReq(atHandle,mode, supportedUEOpt, preferredUEOpt);
					}
				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			break;
		}

		default: /* AT+CCIOTOPT */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/******************************************************************************
 ******
 * F@: ciCSCON - GLOBAL API for GCF AT+CSCON
 *
 */
RETURNCODE_T  ciCSCON(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL cmdValid = FALSE;
	int option;

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CSCON=? */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CSCON: (0-3)" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CSCON? */
		{
			ret = PS_GetSignallingConnectionStausReq(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CSCON= */
		{
			if ( getExtValue( parameter_values_p, 0, &option, TEL_AT_CSCON_0_OPTION_VAL_MIN, TEL_AT_CSCON_0_OPTION_VAL_MAX, TEL_AT_CSCON_0_OPTION_VAL_DEFAULT) == TRUE )
			{
				cmdValid = TRUE;
				ret = PS_SetConfigSignallingConnectionOptionReq(atHandle,(UINT8)option);
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			break;
		}

		default: /* AT+CSCON */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/******************************************************************************
 ******
 * F@: ciCIPCA - GLOBAL API for GCF AT+CIPCA
 *
 */
RETURNCODE_T  ciCIPCA(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL cmdValid = FALSE;
	int option, attachWithoutPDN;

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CIPCA=? */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CIPCA: (0-3),(0-1)" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CIPCA? */
		{
			ret = PS_GetInitialPdpActivationOptReq(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CIPCA= */
		{
			if ( getExtValue( parameter_values_p, 0, &option, TEL_AT_CIPCA_0_OPTION_VAL_MIN, TEL_AT_CIPCA_0_OPTION_VAL_MAX, TEL_AT_CIPCA_0_OPTION_VAL_DEFAULT) == TRUE )
			{

				if ( getExtValue( parameter_values_p, 1,&attachWithoutPDN, TEL_AT_CIPCA_1_ATTACH_WITHOUT_PDN_VAL_MIN, TEL_AT_CIPCA_1_ATTACH_WITHOUT_PDN_VAL_MAX, TEL_AT_CIPCA_1_ATTACH_WITHOUT_PDN_VAL_DEFAULT) == TRUE )
				{
					cmdValid = TRUE;
					ret = PS_SetInitialPdpActivationOptReq(atHandle,option,attachWithoutPDN);
				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			break;
		}

		default: /* AT+CIPCA */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/******************************************************************************
 ******
 * F@: ciCABTSR - GLOBAL API for GCF AT+CABTSR
 *
 */
RETURNCODE_T  ciCABTSR(            const utlAtParameterOp_T op,
				       const char                      *command_name_p,
				       const utlAtParameterValue_P2c parameter_values_p,
				       const size_t num_parameters,
				       const char                      *info_text_p,
				       unsigned int                    *xid_p,
				       void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL cmdValid = FALSE;
	int option;

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CABTSR=? */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CABTSR:(0-1)" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CABTSR? */
		{
			ret = PS_GetApnBackoffTimerStatusReq(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CABTSR= */
		{
			if ( getExtValue( parameter_values_p, 0, &option, TEL_AT_CABTSR_STATUS_VAL_MIN, TEL_AT_CABTSR_STATUS_VAL_MAX, TEL_AT_CABTSR_STATUS_VAL_DEFAULT) == TRUE )
			{
				cmdValid = TRUE;
				ret = PS_SetApnBackoffTimerStatusReq(atHandle,option);
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			break;
		}

		default: /* AT+CABTSR */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciCABTRDP - GLOBAL API for GCF AT+CABTRDP  -command
 *
 */
RETURNCODE_T  ciCABTRDP(            const utlAtParameterOp_T        op,
									  const char                      *command_name_p,
									  const utlAtParameterValue_P2c   parameter_values_p,
									  const size_t                    num_parameters,
									  const char                      *info_text_p,
									  unsigned int                    *xid_p,
									  void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32                           cid=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	CiPsPrimReadApnBackoffTimerDynParaReq req;
	CHAR apnBuf[TEL_AT_CABTRDP_APN_STR_MAX_LEN];
	INT16 apnLen;
	*xid_p = atHandle;

	memset(&req, 0, sizeof(req));

	switch(op)
	{
		case TEL_EXT_TEST_CMD:    /* AT+CABTRDP=? */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+CABTRDP:(0-1),(0-1),(0-1)" );
			break;
		}
		case TEL_EXT_SET_CMD:      /* AT+CABTRDP= */
		{
            if(parameter_values_p[0].is_default == TRUE)
            {
                req.apnPresent=FALSE;
				ret = PS_ReadApnBackoffTimerDynPara(atHandle, &req);

            }
            else if ( getExtString( parameter_values_p, 0, apnBuf, TEL_AT_CABTRDP_APN_STR_MAX_LEN-1, &apnLen, NULL) == TRUE )
            {
            	req.apnPresent=TRUE;
				req.apn.len = apnLen;
				memcpy(req.apn.valStr,apnBuf,apnLen);
				req.apn.valStr[apnLen]='\0';
				ret = PS_ReadApnBackoffTimerDynPara(atHandle, &req);
            }
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			}

			break;
		}
		case TEL_EXT_ACTION_CMD:
		case TEL_EXT_GET_CMD:	 /* AT+CABTRDP?  */
		default:
		{
	        ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciCGAPNRC - GLOBAL API for GCF AT+CGAPNRC  -command
 *
 */
RETURNCODE_T  ciCGAPNRC(            const utlAtParameterOp_T        op,
									  const char                      *command_name_p,
									  const utlAtParameterValue_P2c   parameter_values_p,
									  const size_t                    num_parameters,
									  const char                      *info_text_p,
									  unsigned int                    *xid_p,
									  void                            *arg_p)
{
	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	CiPsPrimGetApnRateControlReq req;
	int cid;
	*xid_p = atHandle;

    memset(&req, 0, sizeof(req));

	switch(op)
	{

		case TEL_EXT_GET_CMD:    /* AT+CGAPNRC?  */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, "+CGAPNRC: (<cid>), (<additional_exception_reports>),(<uplink_time_uint>),(<maximum_uplink_rate>)\r\n");
			break;
		}
		case TEL_EXT_TEST_CMD:    /* AT+CGAPNRC=? */
		{
			ret = PS_GetApnRateControlActCid(atHandle);
			break;
		}
		case TEL_EXT_SET_CMD:      /* AT+CGAPNRC= */
		{
            if(parameter_values_p[0].is_default == TRUE)
            {
                req.cidPresent=FALSE;
				ret = PS_GetApnRateControl(atHandle, &req);

            }
			else if ( getExtValue( parameter_values_p, 0, &cid, TEL_AT_CGAPNRC_CID_VAL_MIN, TEL_AT_CGAPNRC_CID_VAL_MAX, TEL_AT_CGAPNRC_CID_VAL_DEFAULT) == TRUE )
            {
            	if (( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
				{
					/* CI - SAC use cid range starting from 0 */
					cid--;
					req.cidPresent = TRUE;
					req.cid = cid;
				}
				else
				{
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
					break;
				}
				ret = PS_GetApnRateControl(atHandle, &req);
            }
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			}

			break;
		}
		case TEL_EXT_ACTION_CMD:
		default:
		{
	        ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
			break;
		}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#ifdef LWIP_IPNETBUF_SUPPORT
/************************************************************************************
 * F@: ciNetActivate - GLOBAL API for GCF AT*NETACT -command
 *
 */
RETURNCODE_T  ciNetActivate(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	//UNUSEDPARAM(command_name_p)
	//UNUSEDPARAM(num_parameters)
	//UNUSEDPARAM(info_text_p)

	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 netState;
	INT32                                 cid;
	INT32 					type;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

      DBGMSG("ciNetActivate: atHandle = %d, op=%d", atHandle, op);

	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*NETACT?  */
	{
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		break;
	}

	case TEL_EXT_TEST_CMD:    /* AT*NETACT=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETACT: (0/1, cid, type)");
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*NETACT= */
	{
		BOOL cmdValid = FALSE;
		/*
		**  Parse the state.
		*/
		if ( getExtValue( parameter_values_p, 0, (int *)&netState, TEL_AT_CGACT_0_STATE_VAL_MIN, TEL_AT_CGACT_0_STATE_VAL_MAX, TEL_AT_CGACT_0_STATE_VAL_DEFAULT ) == TRUE )
		{
			/* get the <cid> parameter */
			if ( getExtValue(parameter_values_p, 1, (int *)&cid, TEL_AT_CGACT_1_CID_VAL_MIN, TEL_AT_CGACT_1_CID_VAL_MAX, TEL_AT_CGACT_1_CID_VAL_DEFAULT) == TRUE )
			{
				if (( cid > 0 ) && ( cid <= CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ))
				{
					cid--;
					/* get the <type> parameter */
					if ( getExtValue( parameter_values_p, 2, (int *)&type, 0, 1, 1 ) == TRUE )
					{
						cmdValid = TRUE;
#ifdef SUPPORT_CM_DUSTER_DIALER
						configureLwipNetif(netState, cid, type);
#endif
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
					}
				}
			}
		}
		if(!cmdValid)
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*NETACT*/
	default:
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
		break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

/************************************************************************************
 * F@: ciNetifRef - GLOBAL API for GCF AT*NETREF -command
 *
 */
RETURNCODE_T  ciNetifRef(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	//UNUSEDPARAM(command_name_p)
	//UNUSEDPARAM(num_parameters)
	//UNUSEDPARAM(info_text_p)

	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 cid;
	INT32					ref = 0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	CHAR atRspBuf[100];
	*xid_p = atHandle;

      DBGMSG("ciNetifRef: atHandle = %d, op=%d", atHandle, op);

	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*NETREF?  */
	{
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		break;
	}

	case TEL_EXT_TEST_CMD:    /* AT*NETREF=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETREF: (cid)");
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*NETREF= */
	{
		BOOL cmdValid = FALSE;
		/* get the <cid> parameter */
		if ( getExtValue( parameter_values_p, 0, (int *)&cid, TEL_AT_CGACT_1_CID_VAL_MIN, TEL_AT_CGACT_1_CID_VAL_MAX, TEL_AT_CGACT_1_CID_VAL_DEFAULT ) == TRUE )
		{
			if (( cid > 0 ) && ( cid <= CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ))
			{
				cmdValid = TRUE;
				cid--;
#ifdef SUPPORT_CM_DUSTER_DIALER
				ref = getLwipNetifRef(cid);
#endif
				sprintf(atRspBuf, "*NETREF: %d", ref);
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, atRspBuf);
			}

		}
		if(!cmdValid)
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*NETREF*/
	default:
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
		break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

/************************************************************************************
 * F@: ciNetifDns - GLOBAL API for GCF AT*NETDNS -command
 *
 */
RETURNCODE_T  ciNetifDns(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	//UNUSEDPARAM(command_name_p)
	//UNUSEDPARAM(num_parameters)
	//UNUSEDPARAM(info_text_p)

	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 cid;
	INT32					ref;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	CHAR atRspBuf[2*MAX_STRING_LEN + 10] = {0};
	CHAR dns1Str[MAX_STRING_LEN] = {0};
	CHAR dns2Str[MAX_STRING_LEN] = {0};
	INT16 dns1Len, dns2Len;

	*xid_p = atHandle;

      DBGMSG("ciNetifDns: atHandle = %d, op=%d", atHandle, op);

	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*NETDNS?  */
	{
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		break;
	}

	case TEL_EXT_TEST_CMD:    /* AT*NETDNS=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETDNS: cid");
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*NETDNS= */
	{
		BOOL cmdValid = FALSE;
		/* get the <cid> parameter */
		if ( getExtValue( parameter_values_p, 0, (int *)&cid, TEL_AT_CGACT_1_CID_VAL_MIN, TEL_AT_CGACT_1_CID_VAL_MAX, TEL_AT_CGACT_1_CID_VAL_DEFAULT ) == TRUE )
		{
			if (( cid > 0 ) && ( cid <= CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ))
			{
				cid--;
				/* If only the CID is included, we should query this dns info */
				if ( parameter_values_p[1].is_default && parameter_values_p[2].is_default)
				{
#ifdef SUPPORT_CM_DUSTER_DIALER
					if (getLwipNetifDns(cid, dns1Str, dns2Str) == 0)
					{
						sprintf(atRspBuf, "*NETDNS: %s,%s", dns1Str, dns2Str);
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, atRspBuf);
					}
					else
#endif
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);

					break;
				}

				if( getExtString( parameter_values_p, 1, dns1Str, MAX_STRING_LEN, &dns1Len , NULL) == TRUE )
				{
					if( getExtString( parameter_values_p, 2, dns2Str, MAX_STRING_LEN, &dns2Len , NULL) == TRUE )
					{
#ifdef SUPPORT_CM_DUSTER_DIALER
						if (setLwipNetifDns(cid, dns1Str, dns2Str) == 0)
#endif
						{
							cmdValid = TRUE;
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
						}
					}
				}
			}
		}
		if(!cmdValid)
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*NETDNS*/
	default:
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
		break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

/************************************************************************************
 * F@: ciNetif - GLOBAL API for GCF AT*NETIF -command
 *
 */
RETURNCODE_T  ciNetif(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	//UNUSEDPARAM(command_name_p)
	//UNUSEDPARAM(num_parameters)
	//UNUSEDPARAM(info_text_p)

	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 cid;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

      DBGMSG("ciNetif: atHandle = %d, op=%d", atHandle, op);

	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*NETIF?  */
	{
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		break;
	}

	case TEL_EXT_TEST_CMD:    /* AT*NETIF=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETIF: cid");
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*NETIF= */
	{
		BOOL cmdValid = FALSE;
		/* get the <cid> parameter */
		if ( getExtValue( parameter_values_p, 0, (int *)&cid, TEL_AT_CGACT_1_CID_VAL_MIN, TEL_AT_CGACT_1_CID_VAL_MAX, TEL_AT_CGACT_1_CID_VAL_DEFAULT ) == TRUE )
		{
			if (( cid > 0 ) && ( cid <= CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ))
			{
				cid--;
				cmdValid = TRUE;
#ifdef SUPPORT_CM_DUSTER_DIALER
				if (lwipNetifExist(cid))
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETIF: 1");
				else
#endif
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETIF: 0");
			}
		}
		if(!cmdValid)
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*NETIF*/
	default:
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
		break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}


/************************************************************************************
 * F@: ciNetif - GLOBAL API for GCF AT*NETIFCM -command
 *
 */
RETURNCODE_T  ciNetifCm(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	//UNUSEDPARAM(command_name_p)
	//UNUSEDPARAM(num_parameters)
	//UNUSEDPARAM(info_text_p)

	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 mode;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	CHAR *str = NULL;
	*xid_p = atHandle;

      DBGMSG("ciNetifCm: atHandle = %d, op=%d", atHandle, op);

	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*NETIFCM?  */
	{
		CHAR rspBuf[100] = {0};

#ifdef PSM_ENABLE
		str = (CHAR *)psm_get_wrapper("wan", NULL, "cm_set_netif");
		if (str)
		{
			snprintf(rspBuf, 100, "*NETIFCM: %s", str);
			free(str);
			str = NULL;
		}
		else
			snprintf(rspBuf, 100, "*NETIFCM: 1");
#endif

		ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, rspBuf);
		break;
	}

	case TEL_EXT_TEST_CMD:    /* AT*NETIFCM=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*NETIFCM: mode (1: cm set netif, 0: cm not set netif)");
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*NETIFCM= */
	{
		BOOL cmdValid = FALSE;
		/* get the <mode> parameter */
		if ( getExtValue( parameter_values_p, 0, (int *)&mode, 0, 1, 0 ) == TRUE )
		{

#ifdef PSM_ENABLE
			if (mode)
				psm_set_wrapper("wan", NULL, "cm_set_netif", "1");
			else
				psm_set_wrapper("wan", NULL, "cm_set_netif", "0");

			psm_commit__();
#endif

			cmdValid = TRUE;
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		}
		if(!cmdValid)
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*NETIFCM*/
	default:
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
		break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

/************************************************************************************
 * F@: ciMultiPdpSameApn - GLOBAL API for GCF AT*MPSAPN -command
 *
 */
RETURNCODE_T  ciStarMultiPdpSameApn(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	//UNUSEDPARAM(command_name_p)
	//UNUSEDPARAM(num_parameters)
	//UNUSEDPARAM(info_text_p)

	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 mode;
	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	CHAR *str = NULL;
	*xid_p = atHandle;

      DBGMSG("ciNetifCm: atHandle = %d, op=%d", atHandle, op);

	switch (op)
	{
	case TEL_EXT_GET_CMD:    /* AT*MPSAPN?  */
	{
		CHAR rspBuf[100] = {0};
		snprintf(rspBuf, 100, "*MPSAPN: %d", getMultiPdpSameApnFlag());

		ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, rspBuf);
		break;
	}

	case TEL_EXT_TEST_CMD:    /* AT*MPSAPN=? */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*MPSAPN: (0: same APN only one pdp, 1: same APN multi pdp, others: invalid)");
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*MPSAPN= */
	{
		BOOL cmdValid = FALSE;
		/* get the parameter */
		if ( getExtValue( parameter_values_p, 0, (int *)&mode, 0, 1, 0 ) == TRUE )
		{
			setMultiPdpSameApnFlag((UINT8)mode);
#ifdef SUPPORT_CM_DUSTER_DIALER
			sendSetRemapFlagMsg((UINT8)mode);
#endif
#ifdef PSM_ENABLE
			psm_commit__();
#endif
			cmdValid = TRUE;
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
		}
		if(!cmdValid)
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*MPSAPN*/
	default:
		ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
		break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

#ifdef SUPPORT_CM_DUSTER_DIALER
#if defined(CM_UI_API) && !defined(NO_DIALER)
extern PdpInfo cmPdpInfo[15];
void pdpOnStateChange(UINT8 cid, INT32 state, void *data)
{
	//ATDBGMSG(atcmdsrv,180,"%s: cid %d, state %d", __func__, cid, state);
	CM_Connection_Context *pdpCtx;
	UINT8 ipType;

	if (state == CM_CONNECT_CONSUCCESS)
	{
		//ATDBGMSG(atcmdsrv,181,"%s: network up for cid %d", __func__, cid);
		if (data)
		{
			pdpCtx = (CM_Connection_Context *)data;
			//ATDBGMSG(atcmdsrv,182,"%s: pdpCtx ci_cid %d, IP_Type %d(0:IPV4V6, 1:IP, 2: IPV6), APN [%s]", __func__, pdpCtx->pdpinfo->PrimaryCID, pdpCtx->pdpinfo->IP_Type, pdpCtx->pdpinfo->APN);

			if (pdpCtx->pdpinfo->iptype_dialer > 0)
			{
				//ATDBGMSG(atcmdsrv,183,"%s: auto apn enabled, ip type %d(1:IPV4V6, 2:IP, 3: IPV6)", __func__, pdpCtx->pdpinfo->iptype_dialer);
				ipType = pdpCtx->pdpinfo->iptype_dialer - 1;
			}
			else
				ipType = pdpCtx->pdpinfo->IP_Type;

			if (ipType == CM_IPTYPE_IPV4V6)
			{
				//ATDBGMSG(atcmdsrv,184,"%s: IPV4V6", __func__);
				if (pdpCtx->ip4info)
				{
					//ATDBGMSG(atcmdsrv,185,"%s: IPV4 Info, IP %x, pDNS %x, sDNS %x", __func__, pdpCtx->ip4info->IPAddr, pdpCtx->ip4info->PrimaryDNS, pdpCtx->ip4info->SecondaryDNS);
				}
				if (pdpCtx->ip6info)
				{
					//ATDBGMSG(atcmdsrv,186,"%s: IPV6 Info, IP %x %x %x %x, pDNS %x %x %x %x, sDNS %x %x %x %x", __func__, pdpCtx->ip6info->IPV6Addr[0], pdpCtx->ip6info->IPV6Addr[1],pdpCtx->ip6info->IPV6Addr[2], pdpCtx->ip6info->IPV6Addr[3],
					//																	  pdpCtx->ip6info->PrimaryDNS[0],  pdpCtx->ip6info->PrimaryDNS[1], pdpCtx->ip6info->PrimaryDNS[2],  pdpCtx->ip6info->PrimaryDNS[3],
					//																	  pdpCtx->ip6info->SecondaryDNS[0],  pdpCtx->ip6info->SecondaryDNS[1], pdpCtx->ip6info->SecondaryDNS[2],  pdpCtx->ip6info->SecondaryDNS[3]);
				}

			}
			else if(ipType == CM_IPTYPE_IPV6)
			{
				//ATDBGMSG(atcmdsrv,187,"%s: IPV6", __func__);
				if (pdpCtx->ip6info)
				{
					//ATDBGMSG(atcmdsrv,188,"%s: IPV6 Info, IP %x %x %x %x, pDNS %x %x %x %x, sDNS %x %x %x %x", __func__, pdpCtx->ip6info->IPV6Addr[0], pdpCtx->ip6info->IPV6Addr[1],pdpCtx->ip6info->IPV6Addr[2], pdpCtx->ip6info->IPV6Addr[3],
					//																	  pdpCtx->ip6info->PrimaryDNS[0],  pdpCtx->ip6info->PrimaryDNS[1], pdpCtx->ip6info->PrimaryDNS[2],  pdpCtx->ip6info->PrimaryDNS[3],
					//																	  pdpCtx->ip6info->SecondaryDNS[0],  pdpCtx->ip6info->SecondaryDNS[1], pdpCtx->ip6info->SecondaryDNS[2],  pdpCtx->ip6info->SecondaryDNS[3]);
				}
			}
			else
			{
				//ATDBGMSG(atcmdsrv,189,"%s: IPV4", __func__);
				if (pdpCtx->ip4info)
				{
					//ATDBGMSG(atcmdsrv,190,"%s: IPV4 Info, IP %x, pDNS %x, sDNS %x", __func__, pdpCtx->ip4info->IPAddr, pdpCtx->ip4info->PrimaryDNS, pdpCtx->ip4info->SecondaryDNS);
				}
			}
		}
	}
	else if (state == CM_CONNECT_DISCON)
	{
		//ATDBGMSG(atcmdsrv,191,"%s: network down for cid %d", __func__, cid);

	}
	else
	{
		//ATDBGMSG(atcmdsrv,192,"%s: network not ready with state %d", __func__, state);
	}

}

/************************************************************************************
 * F@: ciSetContext - GLOBAL API for GCF AT*AGDCONT -command
 *
 */
RETURNCODE_T  ciSetCmContext(            const utlAtParameterOp_T        op,
									   const char                      *command_name_p,
									   const utlAtParameterValue_P2c   parameter_values_p,
									   const size_t                    num_parameters,
									   const char                      *info_text_p,
									   unsigned int                    *xid_p,
									   void                            *arg_p)
{
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	INT32 primCid=0;
	CHAR tmpStr[TEL_AT_CGDCONT_1_PDPTYPE_STR_MAX_LEN + ATCI_NULL_TERMINATOR_LENGTH] = "";
	INT16 tmpStrLen = 0;
	UINT8 addrLen = 0;
	INT32 tmpParam = 0;
	CiPsPdpCtx pdpCtxInfo;
	UINT32 atHandle = MAKE_AT_HANDLE( *(TelAtParserID *)arg_p );
	CiPsPdpAddr_convert pdp_addr;
	int isDefault = 0;

	*xid_p = atHandle;

	DBGMSG("ciSetContext: atHandle = %d, op=%d", atHandle, op);

	memset(&pdpCtxInfo, 0, sizeof(pdpCtxInfo));
	memset(&pdp_addr, 0, sizeof(pdp_addr));

	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT*AGDCONT?  */
		{
			INT32 i, size = 0, itemLen = TEL_AT_CGDCONT_2_APN_STR_MAX_LEN + strlen("*AGDCONT:") + 16;
			INT32 totalLen = (TEL_AT_CGDCONT_0_CID_VAL_MAX + 1)*itemLen;
			CHAR *rsp = malloc(totalLen);

			if (!rsp)
				break;

			memset(rsp, 0 , totalLen);

			for (i = 0; i < TEL_AT_CGDCONT_0_CID_VAL_MAX; i++)
			{
				if (cmPdpInfo[i].PrimaryCID == i && strlen(cmPdpInfo[i].APN))
				{
					if (cmPdpInfo[i].IP_Type == CM_IPTYPE_IPV4V6)
						size += snprintf(rsp+size, totalLen - size, "*AGDCONT: %d,\"IPV4V6\",", cmPdpInfo[i].PrimaryCID);
					else if (cmPdpInfo[i].IP_Type == CM_IPTYPE_IPV6)
						size += snprintf(rsp+size, totalLen - size, "*AGDCONT: %d,\"IPV6\",", cmPdpInfo[i].PrimaryCID);
					else
						size += snprintf(rsp+size, totalLen - size, "*AGDCONT: %d,\"IP\",", cmPdpInfo[i].PrimaryCID);

					size += snprintf(rsp+size, totalLen - size, "\"%s\"\r\n", cmPdpInfo[i].APN);

				}
			}

			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, rsp);
			free(rsp);
			rsp = NULL;
			break;
		}
		case TEL_EXT_TEST_CMD:	  /* AT*AGDCONT=? */
		{
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT*AGDCONT= */
		{
			memset(&pdpCtxInfo, 0, sizeof(pdpCtxInfo));

			/* 1  Extract the arguments starting with the CID. */
			if ( getExtValue( parameter_values_p, 0, (int *)&primCid, TEL_AT_CGDCONT_0_CID_VAL_MIN, TEL_AT_CGDCONT_0_CID_VAL_MAX, TEL_AT_CGDCONT_0_CID_VAL_DEFAULT ) != TRUE )
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
				break;
			}

			if (( primCid > 0 ) && ( primCid <= CI_PS_MAX_CID ))
			{
				/* CI - SAC use cid range starting from 0 */
				primCid--;
				pdpCtxInfo.cid = primCid;
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
				break;
			}

			/* If only the CID is included, we should undefine this PDP Context */
			if ( parameter_values_p[1].is_default && parameter_values_p[2].is_default && parameter_values_p[3].is_default
					&& parameter_values_p[4].is_default && parameter_values_p[5].is_default )
			{
				//ret = PS_DeleteGPRSContext(atHandle, primCid);
				memset(&cmPdpInfo[primCid], 0, sizeof(PdpInfo));
				break;
			}

			/* 2.  Determine the PDP type.     */
			if ( getExtString( parameter_values_p, 1, tmpStr, TEL_AT_CGDCONT_1_PDPTYPE_STR_MAX_LEN, &tmpStrLen, (CHAR *)TEL_AT_CGDCONT_1_PDPTYPE_STR_DEFAULT) == TRUE )
			{
				if ( tmpStrLen == 0 )
				{
					/* default value */
					pdpCtxInfo.type = CI_PS_PDP_TYPE_IP;
				}
				else
				{
					if ( (strcmp("IP", (char *)tmpStr) == 0) || (strcmp("ip", (char *)tmpStr) == 0) )
						pdpCtxInfo.type = CI_PS_PDP_TYPE_IP;
					else if ( (strcmp("IPV6", (char *)tmpStr) == 0) || (strcmp("ipv6", (char *)tmpStr) == 0) )
						pdpCtxInfo.type = CI_PS_PDP_TYPE_IPV6;
	                else if ( (strcmp("IPV4V6", (char *)tmpStr) == 0) || (strcmp("ipv4v6", (char *)tmpStr) == 0) )
						pdpCtxInfo.type = CI_PS_PDP_TYPE_IPV4V6;
					else
					{            /* default to IP */
						//pdpCtxInfo.type = CI_PS_PDP_TYPE_IP;
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
						break;
					}
				}
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
				break;
			}

			/*	 ** 3 Assign the APN string.				 */
			if ( getExtString(parameter_values_p, 2, tmpStr, TEL_AT_CGDCONT_2_APN_STR_MAX_LEN, &tmpStrLen, TEL_AT_CGDCONT_2_APN_STR_DEFAULT) == TRUE )
			{
				if ( tmpStrLen != 0 )
				{
					pdpCtxInfo.apnPresent = TRUE;
					pdpCtxInfo.apn.len = tmpStrLen;
					memcpy( pdpCtxInfo.apn.valStr, tmpStr, tmpStrLen );
				}
			}
			else
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
				break;
			}

			if ( getExtValue( parameter_values_p, 3, (int *)&isDefault, 0, 1, 0 ) != TRUE )
			{
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
				break;
			}

		 	ApnInfo_s apnInfo = { 0 };

			apnInfo.cid = pdpCtxInfo.cid + 1;

			if (pdpCtxInfo.type == CI_PS_PDP_TYPE_IPV4V6)
				apnInfo.ipType = CM_IPTYPE_IPV4V6;
			else
				apnInfo.ipType = pdpCtxInfo.type;

			memcpy(apnInfo.apn, pdpCtxInfo.apn.valStr, tmpStrLen);
			apnInfo.apn[tmpStrLen] = '\0';

			apnInfo.isDefault = isDefault;

			CM_RegisterStateChangedCb(apnInfo.cid, pdpOnStateChange);
			if (CM_ConfigApnInfo(&apnInfo) == 0)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			else
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);

			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT*AGDCONT */
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}

invalid:
	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciPDPActivate - GLOBAL API for GCF AT*AGACT -command
 *
 */
RETURNCODE_T  ciCmPDPActivate(            const utlAtParameterOp_T        op,
										const char                      *command_name_p,
										const utlAtParameterValue_P2c   parameter_values_p,
										const size_t                    num_parameters,
										const char                      *info_text_p,
										unsigned int                    *xid_p,
										void                            *arg_p)
{
	RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
	CiReturnCode                          ret = CIRC_FAIL;

	INT32                                 pdpState=0;
	INT32                                 cid=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	DBGMSG("ciPDPActivate: atHandle = %d, op=%d", atHandle, op);

	switch(op)
	{
		case TEL_EXT_GET_CMD:    /* AT*AGACT?  */
		{
			//ret = PS_GetGPRSContextActivatedList(atHandle);
			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT*AGACT=? */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_OK,0,(char *)"*AGACT: (0,1)");
			break;
		}

		case TEL_EXT_SET_CMD:      /* AT*AGACT= */
		{
			BOOL cmdValid = FALSE;
			/*
			 **  Parse the state.
			 */
			if ( getExtValue( parameter_values_p, 0, (int *)&pdpState, TEL_AT_CGACT_0_STATE_VAL_MIN, TEL_AT_CGACT_0_STATE_VAL_MAX, TEL_AT_CGACT_0_STATE_VAL_DEFAULT ) == TRUE )
			{
				/* get the <cid> parameter */
				if ( getExtValue(parameter_values_p, 1, (int *)&cid, TEL_AT_CGACT_1_CID_VAL_MIN, TEL_AT_CGACT_1_CID_VAL_MAX, TEL_AT_CGACT_1_CID_VAL_DEFAULT) == TRUE )
				{
					if (( cid > 0 ) && ( cid <= CI_PS_MAX_CID ))
					{
						cmdValid = TRUE;
						PdpState_s pdp_state = { 0 };

						pdp_state.state = pdpState;
						pdp_state.cid = cid;
						if (CM_SetPdpState(&pdp_state) == 0)
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
						else
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);

					}
				}
			}
			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		case TEL_EXT_ACTION_CMD:   /* AT+CGACT */
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}
#endif

#endif


#ifdef ATCMD_PDP_CONTEXT
/************************************************************************************
 * F@: ciTELCGCONT - GLOBAL API for test AT*CGDCONT -command
 *
 */
RETURNCODE_T  ciTELCGCONT(            const utlAtParameterOp_T        op,
									  const char                      *command_name_p,
									  const utlAtParameterValue_P2c   parameter_values_p,
									  const size_t                    num_parameters,
									  const char                      *info_text_p,
									  unsigned int                    *xid_p,
									  void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T                    rc = INITIAL_RETURN_CODE;
	CiReturnCode                    ret = CIRC_FAIL;
	INT32                           cid;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );

	*xid_p = atHandle;

	telPdpInfo *info = NULL;
	char tmpBuf[500];
	char TempBuf[64];

	//DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	switch(op)
	{
	case TEL_EXT_GET_CMD:    /* AT*CGDCONT?  */
	{
		sprintf((char *)tmpBuf, "*CGDCONT: (<p_cid>),(<apn>),(<source_addr and subnet_mask>),(<gw_addr>), (<DNS_prim_addr>), (<DNS_sec_addr>)r\n");
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)tmpBuf);
		break;
	}

	case TEL_EXT_SET_CMD:      /* AT*CGDCONT= */
	{
		/*
		 **  Extract the arguments starting with the CID.
		 */
		if((getExtValue(parameter_values_p, 0, (int *)&cid, TEL_AT_CGEQREQ_0_CID_VAL_MIN, TEL_AT_CGEQREQ_0_CID_VAL_MAX, TEL_AT_CGEQREQ_0_CID_VAL_DEFAULT) == TRUE) &&
			( cid > 0 ) && ( cid <= CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM ))
		{
			info = telGetPdpInfo(cid);

			if (info)
			{
				unsigned char* valData;
				UINT32 hAddr;
				char ipp4[CI_PS_PDP_IP_V4_SIZE] = { '\0' };
				char ipp6[CI_PS_PDP_IP_V6_SIZE] = { '\0' };

				memset(tmpBuf, 0, 500);
				memset(TempBuf, 0, 64);

				char *p = tmpBuf;
				p += sprintf(p, "*CGCONT: %d,", cid );

				if ( strlen(info->apnInfo.apn))
				{
					getPdpTypeStr( (info->apnInfo.ipType == 0)?CI_PS_PDP_TYPE_IPV4V6:info->apnInfo.ipType, TempBuf );
				}
				else
				{
					getPdpTypeStr( CI_PS_PDP_TYPE_IP, TempBuf );
				}
				p += sprintf(p, "%s,", TempBuf);

				if ( strlen(info->apnInfo.epsApn))
				{
					getPdpTypeStr( (info->apnInfo.epsIpType == 0)?CI_PS_PDP_TYPE_IPV4V6:info->apnInfo.epsIpType, TempBuf );
				}
				else
				{
					getPdpTypeStr( CI_PS_PDP_TYPE_IP, TempBuf );
				}

				p += sprintf(p, "%s,", TempBuf);

				/* CGDCONT APN */
				if ( strlen(info->apnInfo.apn))
				{
					strlcpy( TempBuf, info->apnInfo.apn,sizeof(TempBuf));
				}
				else
				{
					TempBuf[0] = '\0';
				}
				p += sprintf(p, "\"%s\",", TempBuf);

				/* EPS APN */
				if ( strlen(info->apnInfo.epsApn))
				{
					strlcpy( TempBuf, info->apnInfo.epsApn,sizeof(TempBuf));
				}
				else
				{
					TempBuf[0] = '\0';
				}

				p += sprintf(p, "\"%s\"", TempBuf);

				if (info->ip4Info)
				{
					hAddr = (info->ip4Info->IPAddr << 24) | ((info->ip4Info->IPAddr & 0xff00) << 8) |
	       					  ((info->ip4Info->IPAddr & 0xff0000) >> 8) | (info->ip4Info->IPAddr >> 24);

					valData = (unsigned char *)&hAddr;
					convertIpUnit2Str(atHandle, CI_PS_PDP_IPV4, valData, ipp4);
					p += sprintf(p, ",\"%s\"", ipp4);
				}

				if (info->ip6Info)
				{
					valData = (unsigned char*)info->ip6Info;
					convertIpUnit2Str(atHandle, CI_PS_PDP_IPV6_INTERFACE, valData, ipp6);
					p += sprintf(p, ",\"%s\"", ipp6);
				}
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)tmpBuf);
			}
			else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0,  NULL);
		}
		break;
	}

	case TEL_EXT_ACTION_CMD:   /* AT*CGDCONT */
	default:
	{
		ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);
		break;
	}
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}
#endif

#ifdef SUPPORT_CM_DUSTER_DIALER
/************************************************************************************
 * F@: ciMTUConfig - GLOBAL API for AT*MTU -command
 *
 */
RETURNCODE_T  ciMTUConfig(            const utlAtParameterOp_T op,
				      const char                      *command_name_p,
				      const utlAtParameterValue_P2c parameter_values_p,
				      const size_t num_parameters,
				      const char                      *info_text_p,
				      unsigned int                    *xid_p,
				      void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	int mtu=0;
	char buf[32] = { 0 };

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Check the operation type.
	*/
	/*mischecked by klocwork*/
	/*klocwork[Inconsistent Case Labels]*/
	switch ( op )
	{
#if 0
		case TEL_EXT_GET_CMD:              /* AT*MTU? */
		{
#ifndef SINGLE_SIM

			if (!GET_SIM1_FLAG(atHandle))
				mtu = getWanMtu(0);
			else
				mtu = getWanMtu(1);
#else
			mtu = getWanMtu(0);
#endif

			snprintf(buf, sizeof(buf) - 1, "*MTU: %d", mtu);
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, NULL, buf);
			break;
		}
#endif
		case TEL_EXT_SET_CMD:              /* AT*MTU= */
		{
			BOOL cmdValid = FALSE;
			int cid;
			if ( getExtValue( parameter_values_p, 0, &cid, 1, 16, 1 ) == TRUE )
			{
				CPUartLogPrintf("%s: cid %d", __func__, cid);
				if (parameter_values_p[1].is_default)
				{
#ifndef SINGLE_SIM

					if (!GET_SIM1_FLAG(atHandle))
						mtu = getWanMtu(0, cid - 1);
					else
						mtu = getWanMtu(1, cid - 1);
#else
					mtu = getWanMtu(0, cid - 1);
#endif

					snprintf(buf, sizeof(buf) - 1, "*MTU: %d", mtu);
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, NULL, buf);
					break;
				}

				if ( getExtValue( parameter_values_p, 1, &mtu, 0, 1500, 0 ) == TRUE )
				{
					cmdValid = TRUE;

					if (!GET_SIM1_FLAG(atHandle))
						setWanMtu(0, cid - 1, mtu, TRUE);
					else
						setWanMtu(1, cid - 1, mtu, TRUE);

					ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
				}
			}

			if(!cmdValid)
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);

			break;
		}

		case TEL_EXT_TEST_CMD:    /* AT*MTU=? */
		{
			snprintf(buf, sizeof(buf) - 1, "*MTU:(1-16)[, (0-1500)]");
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, NULL, buf);
			break;
		}

		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
#endif

#endif

/************************************************************************************
 * F@: ciStarReturnIp - GLOBAL API for GCF AT*GETIP -command
 *
 */
utlReturnCode_T  ciStarReturnIp( const utlAtParameterOp_T        op,
							 const char                      *command_name_p,
							 const utlAtParameterValue_P2c   parameter_values_p,
							 const size_t                    num_parameters,
							 const char                      *info_text_p,
							 unsigned int                    *xid_p,
							 void                            *arg_p)
{
	CiReturnCode ret = CIRC_FAIL;
	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	INT32 index=0;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	*xid_p = atHandle;

	switch (op)
	{
		case TEL_EXT_SET_CMD: /*AT*GETIP=cid*/
			if ( getExtValue( parameter_values_p, 0, (int *)&index, TEL_AT_IP_INDEX_VAL_MIN, TEL_AT_IP_INDEX_VAL_MAX, TEL_AT_IP_INDEX_VAL_DEFAULT ) == TRUE )
			{
				index--;
				ret = PS_GetGPRSContextIPExt(atHandle, index);
			}
			else
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}
	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

void _getPdpErrCauseString( CiPsRc cause, char *sBuf )
{
	char *tBuf = NULL;

	switch (cause)
	{
	case CIRC_PS_ILLEGAL_MS:
		tBuf = "3 Illegal MS";
		break;	
	case CIRC_PS_ILLEGAL_ME:
		tBuf = "6 Illegal ME";
		break;	
	case CIRC_PS_GPRS_SERVICES_NOT_ALLOWED:
		tBuf = "7 GPRS service not allowed";
		break;		
	case CIRC_PS_OPER_DETERMINED_BARRING:
		tBuf = "8 Operator Determined Barring";
		break;
	case CIRC_PS_DETACH:
		tBuf = "10 implicitly detached";
		break;	
	case CIRC_PS_PLMN_NOT_ALLOWED:
		tBuf = "11 PLMN not allowed";
		break;
	case CIRC_PS_LA_NOT_ALLOWED:
		tBuf = "12 Location area not allowed";
		break;
	case CIRC_PS_ROAMING_NOT_ALLOWED:
		tBuf = "13 Roaming not allowed in this location area";
		break;
	case CIRC_PS_MSC_NOT_REACH:
		tBuf = "16 MSC temporarily not reachable";
		break;
	case CIRC_PS_NW_CONGESTION:
		tBuf = "22 Congestion";
		break;
	// SM reject cause (24.008)
	case CIRC_PS_SM_LLC_OR_SNDCP_FAILURE:
		tBuf = "25 LLC OR SNDCP FAILURE";
		break;	
	case CIRC_PS_SM_INSUFFIC_RESOURCES:
		tBuf = "26 Insufficient resources";
		break;
	case CIRC_PS_SM_MISSING_OR_UNKNOWN_APN:
		tBuf = "27 Missing or unknown APN";
		break;
	case CIRC_PS_SM_UNKNOWN_PDP_ADDR_OR_TYPE:
		tBuf = "28 Unknown PDP address or type";
		break;
	case CIRC_PS_SM_USER_AUTH_FAILED:
		tBuf = "29 user authentication failed";
		break;		
	case CIRC_PS_SM_ACTIV_REJ_BY_GGSN:
		tBuf = "30 Activation rejected by GGSN";
		break;
	case CIRC_PS_SM_ACTIV_REJ_UNSPECIFIED:
		tBuf = "31 Activation rejected, unspecified";
		break;
	case CIRC_PS_SM_SERVICE_OPT_NOT_SUPPORTED:
		tBuf = "32 Service option not supported";
		break;
	case CIRC_PS_SM_SERVICE_OPT_NOT_SUBSCRIBED:
		tBuf = "33 Requested service option not subscribed";
		break;
	case CIRC_PS_SM_SERVICE_OPT_TEMP_OUT_OF_ORDER:
		tBuf = "34 Service option temporarily out of order";
		break;
	case CIRC_PS_SM_NSAPI_ALREADY_USED:
		tBuf = "35 NSAPI in use";
		break;
	case CIRC_PS_SM_REGULAR_DEACTIVATION:
		tBuf = "36 Regular deactivation";
		break;	
	case CIRC_PS_SM_QOS_NOT_ACCEPTED:
		tBuf = "37 QOS not accepted";
		break;
	case CIRC_PS_SM_NETWORK_FAILURE:
		tBuf = "38 Network failure";
		break;
	case CIRC_PS_SM_REACTIVATION_REQUIRED:
		tBuf = "39 Reactivation required";
		break;
	case CIRC_PS_SM_FEATURE_NOT_SUPPORTED:
		tBuf = "40 Feature not supported";
		break;	
	case CIRC_PS_SM_SEMANTIC_ERROR_IN_TFT_OPERATION:
		tBuf = "41 Semantic error in the TFT operation";
		break;
	case CIRC_PS_SM_SYNTACTICAL_ERROR_IN_TFT_OPERATION:
		tBuf = "42 Syntactical error in the TFT operation";
		break;
	case CIRC_PS_SM_UNKNOWN_PDP_CONTEXT:
		tBuf = "43 Unknown pdp context";
		break;
	case CIRC_PS_SM_SEMANTIC_ERRORS_IN_PACKET_FILTER:
		tBuf = "44 Semantic error in packet filter";
		break;
	case CIRC_PS_SM_SYNTACTICAL_ERRORS_IN_PACKET_FILTER:
		tBuf = "45 Syntactical error in packet filter";
		break;
	case CIRC_PS_SM_PDP_CONTEXT_WITHOUT_TFT_ALREADY_ACTIVATED:
		tBuf = "46 Pdp context without TFT already activated";
		break;
	case CIRC_PS_ESM_LAST_PDN_DISCONNECTION_NOT_ALLOWED:
		tBuf = "49 Last PDN disconnection not allowed";
		break;
	case CIRC_PS_SM_PDP_TYPE_IPV4_ONLY_ALLOWED:
		tBuf = "50 Only IPv4 allowed";
		break;
	case CIRC_PS_SM_PDP_TYPE_IPV6_ONLY_ALLOWED:
		tBuf = "51 Only IPv6 allowed";
		break;
	case CIRC_PS_SM_SINGLE_ADDRESS_BEARERS_ONLY_ALLOWED:
		tBuf = "52 Only single bearer allowed";
		break;
	case CIRC_PS_SM_INVALID_TI_VALUE:
		tBuf = "81 Invalid TI value";
		break;
	case CIRC_PS_SM_SEMANTICALLY_INCORRECT_MSG:
		tBuf = "95 Semantically incorrect msg";
		break;
	case CIRC_PS_SM_INVALID_MAND_INFORMATION:
		tBuf = "96 Invalid mand information";
		break;
	case CIRC_PS_SM_MSG_TYPE_NONEXIST_OR_NOT_IMP:
		tBuf = "97 Msg type nonexist or not imp";
		break;
	case CIRC_PS_SM_MSG_TYPE_INCOMPAT_WITH_STATE:
		tBuf = "98 Msg type incompat with state";
		break;
	case CIRC_PS_SM_IE_NONEXIST_OR_NOT_IMP:
		tBuf = "99 IE nonexist or not imp";
		break;
	case CIRC_PS_SM_CONDITIONAL_IE_ERROR:
		tBuf = "100 Conditional IE error";
		break;
	case CIRC_PS_SM_MSG_INCOMPAT_WITH_STATE:
		tBuf = "101 incompat with state";
		break;
	case CIRC_PS_SM_PROTOCOL_ERROR_UNSPEC:
		tBuf = "111 Protocol error, unspecified";
		break;
	case CIRC_PS_SM_APN_RESTRICTION:
		tBuf = "112 Apn restriction";
		break;
	// ESM cause, 24.301 - *******
	case CIRC_PS_ESM_PTI_MISMATCH:
		tBuf = "47 Pti mismatch";
		break;
	case CIRC_PS_ESM_ESM_INFORMATION_NOT_RECEIVED:
		tBuf = "53 Information not received";
		break;
	case CIRC_PS_ESM_PDN_CONNECTION_DOES_NOT_EXIST:
		tBuf = "54 Pdn connection does not exist";
		break;
	case CIRC_PS_ESM_MULTIPLE_PDN_CONNECTIONS_FOR_A_GIVEN_APN_NOT_ALLOWED:
		tBuf = "55 Multiple pdn connections for a given apn not allowed";
		break;
	case CIRC_PS_ESM_COLLISION_WITH_NETWORK_INITIATED_REQUEST:
		tBuf = "56 Collision with network initiated request";
		break;
	case CIRC_PS_ESM_UNSUPPORTED_QCI_VALUE:
		tBuf = "59 Unsupported qci value";
		break;
	case CIRC_PS_UNSPECIFIED_ERROR:
		tBuf = "148 Unspecified GPRS error";
		break;
	case CIRC_PS_PDP_AUTHEN_FAILURE:
		tBuf = "149 PDP authentication failure";
		break;
	case CIRC_PS_INVALID_MS_CLASS:
		tBuf = "150 Invalid mobile class";
		break;
	case CIRC_PS_INFO_UNAVAILABLE:
		tBuf = "200 Requested information is unavailable";
		break;
	case CIRC_PS_ALREADY_PROCESSING:
		tBuf = "201 The requested command is already being processed";
		break;
	case CIRC_PS_BUSY_WITH_OTHER_JOB:
		tBuf = "202 CP is busy processing another command";
		break;
	case CIRC_PS_INVALID_PARAMETER:
		tBuf = "203 The requested service primitive has invalid parameters";
		break;
	case CIRC_PS_INVALID_REQ:
		tBuf = "204 The requested service primitive can not be handled at current state";
		break;
	case CIRC_PS_SIM_NOT_READY:
		tBuf = "205 SIM is not ready";
		break;
	case CIRC_PS_ACCESS_DENIED:
		tBuf = "206 Access is denied";
		break;
	case CIRC_PS_INVALID_CID:
		tBuf = "207 Cid is invalid";
		break;
	case CIRC_PS_TFT_PACKET_ERROR_DEFAULT_PDP:
		tBuf = "208 the TFT is invalid for default MT PDP";
		break;
	case CIRC_PS_TFT_PACKET_ERROR_NON_DEFAULT_PDP:
		tBuf = "209 the TFT is invalid for NON default MT PDP";
		break;
	case CIRC_PS_PENDING_SUCCESS:
		tBuf = "210 LTE MO PDP equest completed successfully";
		break;
	case CIRC_PS_ENMERGNECY_BEARER_SERVICE_ALREADY_RUN:
		tBuf = "257 Enmergency bearer service already run";
		break;	
	case CIRC_PS_HANDOVER_FLAG:
		tBuf = "258 Handover flag";
		break;	
	case CIRC_PS_CAUSE_EPS_SERVICE_NOT_AVAILABLE:
		tBuf = "259 Cause EPS service not available";
		break;	
	case CIRC_PS_NOTIFY_REATTACH:
		tBuf = "260 Notify attach";
		break;	
	case CIRC_PS_NOTIFY_DETACH:
		tBuf = "261 Notify detach";
		break;	
	case CIRC_PS_PDN_REQUEST_NEED_RETRY:
		tBuf = "262 PDN request need retry";
		break;
	case CIRC_PS_APN_IS_NOT_AVAILABLE:
		tBuf = "263 APN is not available";
		break;
	case CIRC_PS_EMERGENCY_PDN_REQUEST_CONTAINS_APN:
		tBuf = "264 Emergency PDN request contains APN";
		break;
	case CIRC_PS_ATTACH_FOR_EMERGENCY_BEARER_SERVICE:
		tBuf = "265 Attach for emergency bearer service";
		break;
	case CIRC_PS_PDP_OPERATTION_NOT_ALLOWED:
		tBuf = "267 PDP_OPERATTION_NOT_ALLOWED";
		break;
	case CIRC_PS_PDP_INPUT_PARAM_INVALID:
		tBuf = "268 PDP input param invalid";
		break;
	case CIRC_PS_T3396_RUNNING:
		tBuf = "269 T3396 running";
		break;
	case CIRC_PS_TIMER_OUT_ERROR:
		tBuf = "270 Timer out error";
		break;
	case CIRC_PS_NO_FREE_NSAPIS:
		tBuf = "336 No free nsapis";
		break;
	case CIRC_PS_GPRS_SERVICE_NOT_AVAILABLE:
		tBuf = "337 GPRS service not available";
		break;
	case CIRC_PS_POWERING_DOWN:
		tBuf = "338 Powering down";
		break;
	case CIRC_PS_FDN_FAILURE:
		tBuf = "339 FDN failure";
		break;
	case CIRC_PS_APN_CHECK_FAILURE:
		tBuf = "340 APN check failure";
		break;
	case CIRC_PS_OPERATION_REJECT_BY_MM:
		tBuf = "512 Operation reject by mm";
		break;
	case CIRC_PS_RPM_REJECT:
		tBuf = "880 the RPM manager rejected the request";
		break;
	case CIRC_PS_PDP_REJECT_DSDS:
		tBuf = "13056 PDP reject on DSDS";
		break;
	case CIRC_PS_NO_CAUSE_SET:
		tBuf = "13312 No cause set";
		break;	
	}

	if (CIRC_PS_PROTOCOL_ERROR_MIN <= cause && cause <= CIRC_PS_PROTOCOL_ERROR_MAX)
		sprintf(sBuf, "%u Protocol errors", cause);
	else if (tBuf != NULL)
		strcpy(sBuf, tBuf);
	else
		sprintf(sBuf, "%u Generic error", cause);
	//DBGMSG(getPdpErrCauseString, "getPdpErrCauseString: %s!\n", sBuf);

	return;
}

/************************************************************************************
 * F@: ciCRUEPOLICY - GLOBAL API for GCF AT+CRUEPOLICY - command
 *
 */
RETURNCODE_T ciCRUEPOLICY(const utlAtParameterOp_T op, const char *command_name_p, const utlAtParameterValue_P2c parameter_values_p, const size_t num_parameters, const char *info_text_p, unsigned int *xid_p, void *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

    int reportFlag;

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	//INFOMSG(ciCRUEPOLICY, "ciCRUEPOLICY: atHandle = %d.\n", atHandle);


	switch ((int)op) {
	case TEL_EXT_TEST_CMD:	/* AT+CRUEPOLICY=? */
		{
            ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"+CRUEPOLICY: (0,1)\r\n");

			break;
		}

	case TEL_EXT_SET_CMD:	/* AT+CRUEPOLICY= */
		{

			if ( getExtValue( parameter_values_p, 0, &reportFlag, TEL_AT_CRUEPOLICY_REPORT_VAL_MIN, TEL_AT_CRUEPOLICY_REPORT_VAL_MAX, TEL_AT_CRUEPOLICY_REPORT_VAL_DEFAULT ) == TRUE )
			{
    			     DBGMSG(ciCRUEPOLICY,"ciCRUEPOLICY: reportFlag is %d.\n", reportFlag);
                     ret = PS_SetUePolicy(atHandle, reportFlag);
			}
            else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );

			break;
		}

	case TEL_EXT_GET_CMD:	/* AT+CRUEPOLICY? */
            ret = PS_GetUePolicy(atHandle);
            break;

	case TEL_EXT_ACTION_CMD:	/* AT+CRUEPOLICY */
	default:
            ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
            break;
	}

	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/************************************************************************************
 * F@: ciCSUEPOLICY - GLOBAL API for GCF AT+CSUEPOLICY - command
 *
 */
CHAR *uePolicyStr = NULL;
INT16 uePolicyStrTotalLength = 0;
INT16 uePolicyStrInfoLength = 0;
extern	UINT16 sendIndex;

#ifndef SINGLE_SIM
CHAR *uePolicyStr_1 = NULL;
INT16 uePolicyStrTotalLength_1 = 0;
INT16 uePolicyStrInfoLength_1 = 0;
extern	UINT16 sendIndex_1;
#endif

RETURNCODE_T ciCSUEPOLICY(const utlAtParameterOp_T op, const char *command_name_p, const utlAtParameterValue_P2c parameter_values_p, const size_t num_parameters, const char *info_text_p, unsigned int *xid_p, void *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	int msgType = 0;
	int length = 1;
	CHAR  	uePolicyClassmarkStr[8] = {0};
	INT16 uePolicyClassmarkStrLength = 0;

	CHAR tmp_str[6] = {0};
	UINT16 *pSendIndex = NULL;
	CHAR *pUePolicyStr = NULL;
	INT16 *pUePolicyStrTotalLength = NULL;
	INT16 *pUePolicyStrInfoLength = NULL;	

#ifndef SINGLE_SIM
	if (!GET_SIM1_FLAG(atHandle)) {
		pSendIndex = &sendIndex;
		pUePolicyStr = uePolicyStr;
		pUePolicyStrTotalLength = &uePolicyStrTotalLength;
		pUePolicyStrInfoLength = &uePolicyStrInfoLength;
	}
	else
	{
		pSendIndex = &sendIndex_1;
		pUePolicyStr = uePolicyStr_1;
		pUePolicyStrTotalLength = &uePolicyStrTotalLength_1;
		pUePolicyStrInfoLength = &uePolicyStrInfoLength_1;

	}
#else
	pSendIndex = &sendIndex;
	pUePolicyStr = uePolicyStr;
	pUePolicyStrTotalLength = &uePolicyStrTotalLength;
	pUePolicyStrInfoLength = &uePolicyStrInfoLength;
#endif


	*xid_p = atHandle;
	//INFOMSG(ciCSUEPOLICY,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	switch ((int)op) {
	case TEL_EXT_TEST_CMD:	/* AT+CSUEPOLICY=? */
		{
            ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"+ciCSUEPOLICY\r\n");
			break;
		}

	case TEL_EXT_SET_CMD:	/* AT+CSUEPOLICY= */
		{
#if 0
			if ( getExtValue( parameter_values_p, 0, &msgType, TEL_AT_CRUEPOLICY_REPORT_VAL_MIN, TEL_AT_CRUEPOLICY_REPORT_VAL_MAX, TEL_AT_CRUEPOLICY_REPORT_VAL_DEFAULT ) == TRUE )
			{
					DBGMSG(ciCSUEPOLICY,"ciCSUEPOLICY: msgType is %d.\n", msgType);

					pUePolicyStr = (CHAR*)utlCalloc(1, (TEL_AT_CSUEPOLICY_1_LENGTH_VAL_MAX+6)<<1);
					if(pUePolicyStr == NULL)
						break;

					//put one byte of msgtype into buffer
					sprintf(tmp_str, "%02X", (UINT8)msgType);
					strcat(pUePolicyStr, tmp_str);

					if(msgType == 0)
					{
						*pUePolicyStrTotalLength = (INT16)getUePolicyStrLength(msgType, 0);
						ret = PS_SendUePolicyReq(atHandle, (int)*pUePolicyStrTotalLength,	pUePolicyStr);
						break;
					}

					if ( getExtValue( parameter_values_p, 1, &length, TEL_AT_CSUEPOLICY_1_LENGTH_VAL_MIN, TEL_AT_CSUEPOLICY_1_LENGTH_VAL_MAX, TEL_AT_CSUEPOLICY_1_LENGTH_VAL_DEFAULT ) == TRUE )
					{
						  DBGMSG(ciCSUEPOLICY,"ciCSUEPOLICY: length is %d.\n", length);
						//put one byte of msgtype into buffer
						sprintf(tmp_str, "%04X", length);
						strcat(pUePolicyStr, tmp_str);

						  if ( getExtString( parameter_values_p, 2, &pUePolicyStr[6], TEL_AT_CSUEPOLICY_2_UEPOLICY_STR_MAX_LEN, pUePolicyStrInfoLength, NULL) == TRUE )
						  {
						  		DBGMSG(ciCSUEPOLICY,"ciCSUEPOLICY: uePolicyInfoStrLength is %d.\n", *pUePolicyStrInfoLength);
								if(msgType == 2)
								{
									if ( getExtString( parameter_values_p, 3, uePolicyClassmarkStr, 2, &uePolicyClassmarkStrLength, NULL) == FALSE )
									{
									  ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
									  break;
									}

									memset(tmp_str, 0 ,sizeof(tmp_str));
									sprintf(tmp_str, "%02X", (UINT8)uePolicyClassmarkStr);
									strcat(pUePolicyStr, tmp_str);
								}

								*pUePolicyStrTotalLength = getUePolicyStrLength(msgType, length);
								ret = PS_SendUePolicyReq(atHandle, *pUePolicyStrTotalLength - *pSendIndex,	pUePolicyStr);
						  }
						  else
						  {
							  ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
							  break;
						  }

					}
					else
					{
					  ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
					  break;
					}


			}
            else
#endif

           	{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL );
				break;
            }

		}

	case TEL_EXT_GET_CMD:	/* AT+CSUEPOLICY? */
	case TEL_EXT_ACTION_CMD:	/* AT+CSUEPOLICY */
	default:
            ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL );
            break;
	}



	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciC5GREG - GLOBAL API for GCF AT+C5GREG  -command
 *
 */
RETURNCODE_T  ciC5GREG(const utlAtParameterOp_T op,
    const char                      *command_name_p,
    const utlAtParameterValue_P2c   parameter_values_p,
    const size_t                    num_parameters,
    const char                      *info_text_p,
    unsigned int                    *xid_p,
    void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T    rc = INITIAL_RETURN_CODE;
	CiReturnCode    ret = CIRC_FAIL;
	UINT32          atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);

	*xid_p = atHandle;

	//INFOMSG(ciC5GREG,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);


	/*
	 **  Check the operation type.
	 */
	switch( op )
	{
		case TEL_EXT_GET_CMD:     /* AT+C5GREG? */
		{
			ret = PS_Get5GRegStatus(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:     /* AT+C5GREG= */
		{
			INT32  regParam;
			if ( getExtValue( parameter_values_p, 0, (int *)(&regParam), CI_PS_NW_REG_IND_DISABLE, CI_PS_NW_REG_IND_ENABLE_MORE_DETAIL, CI_PS_NW_REG_IND_DISABLE ) == TRUE )
			{
				ret = PS_Set5GRegOption(atHandle, regParam);
			}
			else
			{
				ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
			}

			break;
		}

		case TEL_EXT_ACTION_CMD:     /* AT+C5GREG */
		default:
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciC5GQOS - GLOBAL API for GCF AT+C5GQOS	-command
 *
 */
RETURNCODE_T  ciC5GQOS(const utlAtParameterOp_T op,
	const char						*command_name_p,
	const utlAtParameterValue_P2c	parameter_values_p,
	const size_t					num_parameters,
	const char						*info_text_p,
	unsigned int					*xid_p,
	void							*arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	rc = INITIAL_RETURN_CODE;
	CiReturnCode	ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	unsigned int cid = 0;
	unsigned int nrqi = 0;
	unsigned int dl_gfbr = 0;
	unsigned int ul_gfbr = 0;
	unsigned int dl_mfbr = 0;
	unsigned int ul_mfbr = 0;
	BOOL validFlag = FALSE;
	CiPs5GQosProfile *qosProfile = NULL;

	*xid_p = atHandle;
	
	//INFOMSG(ciCQOS,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 **  Check the operation type.
	 */
	switch( op )
	{
		case TEL_EXT_GET_CMD:	  /* AT+C5GQOS? */
		{
			ret = PS_Get5GQos(atHandle);
			break;
		}

		case TEL_EXT_TEST_CMD:	  /* AT+C5GQOS=? */
		{
			//ret = PS_Get5GCapsQos(atHandle);
			ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, 0);
			break;
		}

		case TEL_EXT_SET_CMD:	  /* AT+C5GQOS= */
		{
		    qosProfile     = (CiPs5GQosProfile*)utlCalloc(1, sizeof(CiPs5GQosProfile));
			if(qosProfile == NULL)
				break;

			if ( getExtUValue( parameter_values_p, 0,&cid, TEL_AT_C5GQOS_0_CID_VAL_MIN, TEL_AT_C5GQOS_0_CID_VAL_MAX, TEL_AT_C5GQOS_0_CID_VAL_DEFAULT ) == TRUE )
			{
				if ( getExtUValue( parameter_values_p, 1,&nrqi, TEL_AT_C5GQOS_1_5QI_VAL_MIN, TEL_AT_C5GQOS_1_5QI_VAL_MAX, TEL_AT_C5GQOS_1_5QI_VAL_DEFAULT ) == TRUE )
				{
					if (parameter_values_p[1].is_default)
					{					
						validFlag = TRUE;
						break;
					}
			
					if( (nrqi == 65) || (nrqi == 66) || (nrqi == 67) || (nrqi == 69) || (nrqi == 70))
					{
						ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNSUPPORTED_QCI_VALUE,NULL);
	 					break;
					}
					else if((nrqi > 9)          && (nrqi < 71)   )
					{
						ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
	 					break;
					}
					else if((nrqi == 77)          || (nrqi == 78)        || (nrqi == 81)    )
					{
						ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
	 					break;
					}						
					else if((nrqi > 85)          && (nrqi < 128)   )
					{
						ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
	 					break;
					}
					else if((nrqi != 79)          && (nrqi != 80) && ((nrqi > 9)           || (nrqi < 5)))
					{
						if ( getExtUValue( parameter_values_p, 2,&dl_gfbr, TEL_AT_C5GQOS_2_DLGFBR_VAL_MIN, TEL_AT_CGEQOS_2_DLGFBR_VAL_MAX, TEL_AT_CGEQOS_2_DLGFBR_VAL_DEFAULT ) == TRUE )
						{
							if ( getExtUValue( parameter_values_p, 3,&ul_gfbr, TEL_AT_C5GQOS_3_ULGFBR_VAL_MIN, TEL_AT_CGEQOS_3_ULGFBR_VAL_MAX, TEL_AT_CGEQOS_3_ULGFBR_VAL_DEFAULT ) == TRUE )
							{
								if ( getExtUValue( parameter_values_p, 4,&dl_mfbr, TEL_AT_C5GQOS_4_DLMFBR_VAL_MIN, TEL_AT_CGEQOS_4_DLMFBR_VAL_MAX, TEL_AT_CGEQOS_4_DLMFBR_VAL_DEFAULT ) == TRUE )
								{
									if ( getExtUValue( parameter_values_p, 5,&ul_mfbr, TEL_AT_C5GQOS_5_ULMFBR_VAL_MIN, TEL_AT_CGEQOS_5_ULMFBR_VAL_MAX, TEL_AT_CGEQOS_5_ULMFBR_VAL_DEFAULT ) == TRUE )
									{
										qosProfile->qci = nrqi;
										qosProfile->gbrMbrPresent = 1;
										qosProfile->guaranteedDLRate = dl_gfbr;
										qosProfile->guaranteedULRate = ul_gfbr;										
										qosProfile->maxDLRate = dl_mfbr;
										qosProfile->maxULRate = ul_mfbr;												
										validFlag = TRUE;
									}
									else
										ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);						
								}
								else
									ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);						
							}
							else
								ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);						
						}
						else
							ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
					}
					else  //non-guaranteed bit rate
					{
						qosProfile->qci = nrqi;							
						validFlag = TRUE;
					}						
				}
				else
					ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
			}
			else
				ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);

			if(validFlag)
				ret = PS_Set5GQos(atHandle, (UINT8)(cid-1), qosProfile);

			break;
		}

		default:
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
			break;
		}
	}

	if(qosProfile != NULL)
		utlFree(qosProfile);
		
	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
/*******************************************************************
*  FUNCTION: ciUTTest
*
*  DESCRIPTION: start/stop UT test
*
*  PARAMETERS:
*
*  RETURNS:
*
*******************************************************************/
utlReturnCode_T   ciUTTest( const utlAtParameterOp_T op,
			const char						*command_name_p,
			const utlAtParameterValue_P2c parameter_values_p,
			const size_t num_parameters,
			const char						*info_text_p,
			unsigned int					*xid_p,
			void							*arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T					rc = INITIAL_RETURN_CODE;
	CiReturnCode					ret = CIRC_FAIL;
	int 						oper=0;
	int 						mode=0;
	int 						direction=0;
	int 						maxDlRate=0;

	UINT32 atHandle = MAKE_AT_HANDLE( * (TelAtParserID *) arg_p );
	*xid_p = atHandle;

	//INFOMSG(ciUTTest,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);


	switch(op)
	{
	case TEL_EXT_TEST_CMD:	 /* AT*UTTEST=?	*/
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*UTTEST: <1,0>, <1,2>, <1-3>, <1-0x7FFFFFFF>");

		break;
	}

	case TEL_EXT_SET_CMD:	   /* AT*UTTEST = */
	{

		if(getExtValue(parameter_values_p, 0, &oper, TEL_AT_UTTEST_0_OPERATION_MIN_VALUE, TEL_AT_UTTEST_0_OPERATION_MAX_VALUE, TEL_AT_UTTEST_0_OPERATION_DEFAULT_VALUE) == TRUE)
		{
			if(getExtValue(parameter_values_p, 1, &mode, TEL_AT_UTTEST_1_MODE_MIN_VALUE, TEL_AT_UTTEST_1_MODE_MAX_VALUE, TEL_AT_UTTEST_1_MODE_DEFAULT_VALUE) == TRUE)
			{
				if(getExtValue(parameter_values_p, 2, &direction, TEL_AT_UTTEST_2_DIRECTION_MIN_VALUE, TEL_AT_UTTEST_2_DIRECTION_MAX_VALUE, TEL_AT_UTTEST_2_DIRECTION_DEFAULT_VALUE) == TRUE)
				{
					if(getExtValue(parameter_values_p, 3, &maxDlRate, TEL_AT_UTTEST_3_MAXDLRATE_MIN_VALUE, TEL_AT_UTTEST_3_MAXDLRATE_MAX_VALUE, TEL_AT_UTTEST_3_MAXDLRATE_DEFAULT_VALUE) == TRUE)
					{
							ret = PS_UTTest(atHandle, (UINT8)oper, (UINT8)mode, (UINT8)direction, (UINT32)maxDlRate);
					}
					else
						ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
				}
				else
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
			}
			else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		}
		else
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);

		break;
	}

	case TEL_EXT_GET_CMD:   /* AT*UTTEST? */
	default:
	{
		ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

RETURNCODE_T  ciDefaultNssai(			 const utlAtParameterOp_T op,
					   const char					   *command_name_p,
					   const utlAtParameterValue_P2c parameter_values_p,
					   const size_t num_parameters,
					   const char					   *info_text_p,
					   unsigned int 				   *xid_p,
					   void 						   *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	rc = INITIAL_RETURN_CODE;
	CiReturnCode	ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL			cmdValid = FALSE;
	INT16			nssaiLen, tmpBufLen, i, j;
	UINT8			tmpBuf[TEL_AT_C5GNSSAI_STR_MAX_LEN] = {0};
	UINT8			sNssai[CI_PS_MAX_SNSSAI_NUM][30] = {0};
	CiPsPrimSetDefaultNssaiReq setDefaultNssaiReq;
	*xid_p = atHandle;
	//INFOMSG(ciDefaultNssai,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+C5GNSSAI=? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+C5GNSSAI: (0-16),""" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+C5GNSSAI? */
		{
			ret = PS_GetDefaultNssai(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+C5GNSSAI= */
		{
			if ( getExtValue( parameter_values_p, 0, (int *)(&nssaiLen), TEL_AT_C5GNSSAI_LEN_MIN, TEL_AT_C5GNSSAI_LEN_MAX, TEL_AT_C5GNSSAI_LEN_DEFAULT ) == TRUE )
			{
				if ( getExtString( parameter_values_p, 1, (char *)tmpBuf, TEL_AT_C5GNSSAI_STR_MAX_LEN, &tmpBufLen, TEL_AL_C5GNSSAI_STR_DEFAULT ) == TRUE )
				{
					setDefaultNssaiReq.nssai.len = tmpBufLen;
					memcpy(&setDefaultNssaiReq.nssai.valStr, (CHAR *)tmpBuf, tmpBufLen);

					cmdValid = TRUE;
					ret = PS_SetDefaultNssai(atHandle,&setDefaultNssaiReq);
				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		default: /* AT+C5GNSSAI */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/******************************************************************************
 ******
 * F@: ciPreferredNssai - GLOBAL API for GCF AT+C5GPNSSAI command
 *
 */

RETURNCODE_T  ciPreferredNssai( 		   const utlAtParameterOp_T op,
					   const char					   *command_name_p,
					   const utlAtParameterValue_P2c parameter_values_p,
					   const size_t num_parameters,
					   const char					   *info_text_p,
					   unsigned int 				   *xid_p,
					   void 						   *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	rc = INITIAL_RETURN_CODE;
	CiReturnCode	ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL			cmdValid = FALSE;
	INT16			nssaiLen, tmpBufLen, i, j;
	UINT8			tmpBuf[TEL_AT_C5GNSSAI_STR_MAX_LEN] = {0};
	CiPsPrimSetPreferredNssaiReq setPreferredNssaiReq;
	*xid_p = atHandle;
	//INFOMSG(ciPreferredNssai,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */

	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+C5GPNSSAI=? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+C5GPNSSAI: (0-16),"",(0-16),""" );
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+C5GPNSSAI? */
		{
			ret = PS_GetPreferredNssai(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+C5GPNSSAI= */
		{
			if ( getExtValue( parameter_values_p, 0, (int *)(&nssaiLen), TEL_AT_C5GNSSAI_LEN_MIN, TEL_AT_C5GNSSAI_LEN_MAX, TEL_AT_C5GNSSAI_LEN_DEFAULT ) == TRUE )
			{
				if ( getExtString( parameter_values_p, 1, (char *)tmpBuf, TEL_AT_C5GNSSAI_STR_MAX_LEN, &tmpBufLen, TEL_AL_C5GNSSAI_STR_DEFAULT ) == TRUE )
				{
					setPreferredNssaiReq.nssai.len = tmpBufLen;
					memcpy(&setPreferredNssaiReq.nssai.valStr, (CHAR *)tmpBuf, tmpBufLen);

					cmdValid = TRUE;
					ret = PS_SetPreferredNssai(atHandle,&setPreferredNssaiReq);
				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		default: /* AT+C5GPNSSAI */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/******************************************************************************
 ******
 * F@: ciGetNssai - GLOBAL API for GCF AT+C5GNSSAIRDP command
 *
 */

RETURNCODE_T  ciGetNssai(			 const utlAtParameterOp_T op,
					   const char					   *command_name_p,
					   const utlAtParameterValue_P2c parameter_values_p,
					   const size_t num_parameters,
					   const char					   *info_text_p,
					   unsigned int 				   *xid_p,
					   void 						   *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T		rc = INITIAL_RETURN_CODE;
	CiReturnCode		ret = CIRC_FAIL;
	UINT32				atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	BOOL				cmdValid = FALSE;
	UINT8				tmpBuf[TEL_AT_C5GNSSAI_STR_MAX_LEN] = {0};
	UINT16				tmpBufLen;
	Plmn				plmn;
	CiPsNssaiType		type;
	CiPsPrimGetNssaiReq getNssaiReq;
	*xid_p = atHandle;
	//INFOMSG(ciGetNssai,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 * process operation
	 */


	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+C5GNSSAIRDP=? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK,0,(char *)"+C5GNSSAIRDP: (0-3),(000000-FFFFFF)" );
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+C5GNSSAIRDP= */
		{
			if ( getExtValue( parameter_values_p, 0, (int *)(&type), TEL_AT_C5GNSSAIRDP_TYPE_MIN, TEL_AT_C5GNSSAIRDP_TYPE_MAX, TEL_AT_C5GNSSAIRDP_TYPE_DEFAULT ) == TRUE )
			{
				getNssaiReq.type = (UINT8)type;

				if ( getExtString( parameter_values_p, 1, (char *)tmpBuf, PLMN_NAME_FULL_LENGTH, (INT16 *)(&tmpBufLen), NULL ) == TRUE )
				{
					if(parameter_values_p[1].is_default == TRUE)
					{
						getNssaiReq.plmnIdPresent = FALSE;
					}
					else
					{
						if ( operStringToPlmn((CHAR *)tmpBuf, &plmn, tmpBufLen) == FALSE )
						{
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
							break;
						}
						getNssaiReq.plmnIdPresent = TRUE;
						getNssaiReq.plmnId.CountryCode = plmn.mcc;
						getNssaiReq.plmnId.NetworkCode = plmn.mnc;
						if (tmpBufLen > 5)
						{
							getNssaiReq.plmnId.MncDigit = CIMM_NETOP_THREE_DIGIT_MNC;
						}
						else
						{
							getNssaiReq.plmnId.MncDigit = CIMM_NETOP_TWO_DIGIT_MNC;
						}
					}

					cmdValid = TRUE;
					ret = PS_GetNssai(atHandle,&getNssaiReq);
				}
			}

			if(!cmdValid)
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+C5GNSSAIRDP? */
		default: /* AT+C5GNSSAIRDP */
		{
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/******************************************************************************
******
* F@: ciGetNssai - GLOBAL API for GCF AT*SETUEOSID command
*
*/

RETURNCODE_T  ciSETUEOSID(			const utlAtParameterOp_T op,
					  const char					  *command_name_p,
					  const utlAtParameterValue_P2c parameter_values_p,
					  const size_t num_parameters,
					  const char					  *info_text_p,
					  unsigned int					  *xid_p,
					  void							  *arg_p)
{
   UNUSEDPARAM(command_name_p)
   UNUSEDPARAM(num_parameters)
   UNUSEDPARAM(info_text_p)

   RETURNCODE_T 	   rc = INITIAL_RETURN_CODE;
   CiReturnCode 	   ret = CIRC_FAIL;
   UINT32			   atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
   BOOL 			   cmdValid = FALSE;
   UINT8			   tmpBuf[33] = {0};
   UINT16			   tmpBufLen;
   *xid_p = atHandle;

   //INFOMSG(ciSETUEOSID,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

   /*
	* process operation
	*/

   switch ( op )
   {
	   case TEL_EXT_SET_CMD: /* AT*SETUEOSID= */
	   {
	   	   memset(tmpBuf, 0, sizeof(tmpBuf));
		   if ( getExtString( parameter_values_p, 0, (char *)tmpBuf, 33, (INT16 *)(&tmpBufLen), NULL ) == TRUE )
		   {
		   		if (tmpBufLen != 0 && tmpBufLen%2==0)
					ret = PS_SetUEOSID(atHandle, tmpBuf);
				else
					ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
		   }
		   break;
	   }

	   default: /* AT*SETUEOSID */
	   {
		   ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
		   break;
	   }
   }

   /* handle the return value */
   rc = HANDLE_RETURN_VALUE(ret);
   return(rc);
}

/************************************************************************************
* F@: ciC5GCAPA - GLOBAL API for GCF AT+5GCAPA  -command
*
*/
RETURNCODE_T	ciC5GCAPA(const utlAtParameterOp_T op,
	  const char					  *command_name_p,
	  const utlAtParameterValue_P2c   parameter_values_p,
	  const size_t					  num_parameters,
	  const char					  *info_text_p,
	  unsigned int					  *xid_p,
	  void							  *arg_p)
{
	  UNUSEDPARAM(command_name_p)
	  UNUSEDPARAM(num_parameters)
	  UNUSEDPARAM(info_text_p)

	  RETURNCODE_T	  rc = INITIAL_RETURN_CODE;
	  CiReturnCode	  ret = CIRC_FAIL;
	  UINT32		  atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	  int index = 0;
	  int value = 0;
	  int end = 0;
	  BOOL cmdValid = FALSE;

	  *xid_p = atHandle;
	  //INFOMSG(ciC5GCAPA,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	  /*
	   **  Check the operation type.
	   */
	  switch( op )
	  {

	  case TEL_EXT_GET_CMD: 	/* AT+5GCAPA? */
	  {
		  ret = PS_Get5GCAPA(atHandle);
		  break;
	  }

	  case TEL_EXT_SET_CMD: 	/* AT+5GCAPA= */
	  {
		  if ( getExtValue( parameter_values_p, 0,&index, TEL_AT_5GCAPA_0_INDEX_MIN, TEL_AT_5GCAPA_0_INDEX_MAX, TEL_AT_5GCAPA_0_INDEX_DEFAULT ) == TRUE )
		  {
			  if ( getExtValue( parameter_values_p, 1,&value, TEL_AT_5GCAPA_1_VALUE_MIN, TEL_AT_5GCAPA_1_VALUE_MAX, TEL_AT_5GCAPA_1_VALUE_DEFAULT ) == TRUE )
			  {
				  if ( getExtValue( parameter_values_p, 2,&end, TEL_AT_5GCAPA_2_END_MIN, TEL_AT_5GCAPA_2_END_MAX, TEL_AT_5GCAPA_2_END_DEFAULT ) == TRUE )
				  {
				  		cmdValid = TRUE;
				  }
			  }
		  }

		  if(cmdValid)
			  ret = PS_Set5GCAPA(atHandle, index, value, end);
		  else
			  ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);


		  break;
	  }

	  default:
	  {
		  ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		  break;
	  }
	  }

	  /* handle the return value */
	  rc = HANDLE_RETURN_VALUE(ret);
	  return(rc);
}


/************************************************************************************
* F@: ciCWUS - GLOBAL API for GCF AT+CWUS  -command
*
*/
RETURNCODE_T	  ciCWUS(const utlAtParameterOp_T op,
	const char						*command_name_p,
	const utlAtParameterValue_P2c	parameter_values_p,
	const size_t					num_parameters,
	const char						*info_text_p,
	unsigned int					*xid_p,
	void							*arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	rc = INITIAL_RETURN_CODE;
	CiReturnCode	ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	int mode = 0;
	int condition = 0;

	BOOL cmdValid = FALSE;

	*xid_p = atHandle;
	//INFOMSG(ciCWUS,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 **  Check the operation type.
	 */
	switch( op )
	{

	case TEL_EXT_GET_CMD:	  /* AT+CWUS? */
	{
		ret = PS_GetCwus(atHandle);
		break;
	}

	case TEL_EXT_SET_CMD:	  /* AT+CWUS= */
	{
		if ( getExtValue( parameter_values_p, 0,&mode , TEL_AT_CWUS_0_MODE_MIN, TEL_AT_CWUS_0_MODE_MAX, TEL_AT_CWUS_0_MODE_DEFAULT ) == TRUE )
		{
			if ( getExtValue( parameter_values_p, 1,&condition, TEL_AT_CWUS_1_CONDITION_MIN, TEL_AT_CWUS_1_CONDITION_MAX, TEL_AT_CWUS_1_CONDITION_DEFAULT ) == TRUE )
			{
				cmdValid = TRUE;
			}
		}

		if(cmdValid)
			ret = PS_SetCwus(atHandle, mode, condition);
		else
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);


		break;
	}

	default:
	{
		ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}


/************************************************************************************
* F@: ciCLADN - GLOBAL API for GCF AT+CLADN  -command
*
*/
RETURNCODE_T	  ciCLADN(const utlAtParameterOp_T op,
	const char						*command_name_p,
	const utlAtParameterValue_P2c	parameter_values_p,
	const size_t					num_parameters,
	const char						*info_text_p,
	unsigned int					*xid_p,
	void							*arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	rc = INITIAL_RETURN_CODE;
	CiReturnCode	ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	int reportFlag = 0;

	BOOL cmdValid = FALSE;

	*xid_p = atHandle;
	//INFOMSG(ciCLADN,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 **  Check the operation type.
	 */
	switch( op )
	{

	case TEL_EXT_GET_CMD:	  /* AT+CLADN? */
	{
		ret = PS_GetCladn(atHandle);
		break;
	}

	case TEL_EXT_SET_CMD:	  /* AT+CLADN= */
	{
		if ( getExtValue( parameter_values_p, 0,&reportFlag , TEL_AT_CLADN_0_MODE_MIN, TEL_AT_CLADN_0_MODE_MAX, TEL_AT_CLADN_0_MODE_DEFAULT ) == TRUE )
		{
			cmdValid = TRUE;
		}

		if(cmdValid)
			ret = PS_SetCladn(atHandle, reportFlag);
		else
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);


		break;
	}

	default:
	{
		ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
* F@: ciCMICO - GLOBAL API for GCF AT+CMICO  -command
*
*/
RETURNCODE_T	  ciCMICO(const utlAtParameterOp_T op,
	const char						*command_name_p,
	const utlAtParameterValue_P2c	parameter_values_p,
	const size_t					num_parameters,
	const char						*info_text_p,
	unsigned int					*xid_p,
	void							*arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	rc = INITIAL_RETURN_CODE;
	CiReturnCode	ret = CIRC_FAIL;
	UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	int reportFlag = 0;
	int micoMode = 0;
	INT16			tmpBufLen;
	UINT8			tmpBuf[TEL_AT_CMICO_2_Time_STR_MAX_LEN + 1] = {0};

	BOOL cmdValid = FALSE;

	*xid_p = atHandle;
	//INFOMSG(ciCMICO,"%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	 **  Check the operation type.
	 */
	switch( op )
	{

	case TEL_EXT_GET_CMD:	  /* AT+CMICO? */
	{
		ret = PS_GetCmico(atHandle);
		break;
	}

	case TEL_EXT_SET_CMD:	  /* AT+CMICO= */
	{
		if ( getExtValue( parameter_values_p, 0,&reportFlag , TEL_AT_CMICO_0_REPORT_MIN, TEL_AT_CMICO_0_REPORT_MAX, TEL_AT_CMICO_0_REPORT_DEFAULT ) == TRUE )
		{
			if ( getExtValue( parameter_values_p, 1,&micoMode , TEL_AT_CMICO_1_MODE_MIN, TEL_AT_CMICO_1_MODE_MAX, TEL_AT_CMICO_1_MODE_DEFAULT ) == TRUE )
			{
				if ( getExtString( parameter_values_p, 2, (char *)tmpBuf, TEL_AT_CMICO_2_Time_STR_MAX_LEN, &tmpBufLen, TEL_AT_CMICO_2_Time_STR_DEFAULT ) == TRUE )
				{
					cmdValid = TRUE;
				}
			}
		}

		if(cmdValid)
			ret = PS_SetCmico(atHandle, reportFlag, micoMode, (char *)tmpBuf);
		else
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);

		break;
	}

	default:
	{
		ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

RETURNCODE_T  ciSetSmPduDnReqContainer(const utlAtParameterOp_T 	op,
											  const char					*command_name_p,
											  const utlAtParameterValue_P2c parameter_values_p,
											  const size_t					num_parameters,
											  const char					*info_text_p,
											  unsigned int					*xid_p,
											  void							*arg_p)
{
	RETURNCODE_T						  rc = INITIAL_RETURN_CODE;
	CiReturnCode						  ret = CIRC_FAIL;

	/*
	 *	Put parser index into the variable
	 */
	UINT32 atHandle;
	TelAtParserID sAtpIndex = * (TelAtParserID *) arg_p;
	atHandle = MAKE_AT_HANDLE( sAtpIndex );
	*xid_p = atHandle;
	DBGMSG(ciSetSmPduDnReqContainer, "ciSetSmPduDnReqContainer: atHandle = %d, op=%d", atHandle, op);

	/*
	 **  Check the format of the request.
	 */
	switch(op)
	{
		case TEL_EXT_TEST_CMD:	  /* AT*CDNID=? */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"*CDNID: a string with (0-253) octets\r\n");
			break;
		}

		case TEL_EXT_SET_CMD:	   /* AT*CDNID= */
		{
			int cid;
			CHAR tmpStr[TEL_AT_CDNID_0_DNID_STR_MAX_LEN] = "";
			UINT16 tmpStrLen = 0;

			/* 1  Extract the CID. */
			if(getExtValue(parameter_values_p,
						   0,
						   &cid,
						   TEL_AT_CGDCONT_0_CID_VAL_MIN,
						   TEL_AT_CGDCONT_0_CID_VAL_MAX,
						   TEL_AT_CGDCONT_0_CID_VAL_DEFAULT) != TRUE)
				break;

			if ((cid > 0) && (cid <= CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM))
			{
				cid --;
			}
			else
			{
				break;
			}

			/* 2  Extract the SM PDU DN request container. */
			if (getExtString(parameter_values_p,
							 1,
							 tmpStr,
							 TEL_AT_CDNID_0_DNID_STR_MAX_LEN - ATCI_NULL_TERMINATOR_LENGTH,
							 (INT16 *)(&tmpStrLen),
							 (CHAR *)TEL_AT_CDNID_0_DNID_STR_DEFAULT) == TRUE)
			{
				if (tmpStrLen == 0)
				{
					break;
				}
			}

			ret = PS_SetSmPduDnRequestContainer(atHandle, (UINT8)cid, (UINT8)tmpStrLen, (UINT8 *)tmpStr);
			if(ret == CIRC_FAIL)
				ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_UNKNOWN,NULL);

			break;
		}

		case TEL_EXT_GET_CMD:	 /* AT*CDNID?	*/
		case TEL_EXT_ACTION_CMD:   /* AT*CDNID */
		default:
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
			break;
	}


	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

RETURNCODE_T  ciC5GUSMS(const utlAtParameterOp_T     op,
                                            const char                    *command_name_p,
                                            const utlAtParameterValue_P2c parameter_values_p,
                                            const size_t                  num_parameters,
                                            const char                    *info_text_p,
                                            unsigned int                  *xid_p,
                                            void                          *arg_p)
{
    RETURNCODE_T                          rc = INITIAL_RETURN_CODE;
    CiReturnCode                          ret = CIRC_FAIL;

    /*
    *  Put parser index into the variable
    */
    UINT32 atHandle;
    TelAtParserID sAtpIndex = * (TelAtParserID *) arg_p;
    atHandle = MAKE_AT_HANDLE( sAtpIndex );
    *xid_p = atHandle;
    DBGMSG(ciC5GUSMS0, "ciC5GUSMS: atHandle = %d, op=%d", atHandle, op);

    /*
    **  Check the format of the request.
    */
    switch(op)
    {
      case TEL_EXT_TEST_CMD:    /* AT+C5GUSMS=? */
      {
          ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, (char *)"+C5GUSMS:(0-2),(0-1)\r\n");
          break;
      }

      case TEL_EXT_SET_CMD:      /* AT+C5GUSMS= */
      {
          int option = 0, mode = 0;

          if(getExtValue(parameter_values_p, 0, &option, 0, 2, 2) == TRUE)
          {
                //CPUartLogPrintf("ciC5GUSMS: option = %d", option);
                if(getExtValue(parameter_values_p, 1, &mode, 0, 1, 0) == TRUE)
                {
                    //CPUartLogPrintf("ciC5GUSMS: mode = %d", mode);
                    ret = PS_SetC5GUSMS(atHandle, option, mode);
                }
                else
                {
                    ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
                }
          }
          else
          {
                ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, 0);
          }
          break;
      }

      case TEL_EXT_GET_CMD:    /* AT+C5GUSMS?   */
      {
          ret = PS_GetC5GUSMS(atHandle);
          break;
      }
      case TEL_EXT_ACTION_CMD:   /* AT+C5GUSMS */
      default:
          ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, 0);
          break;
    }


    /* handle the return value */
    rc = HANDLE_RETURN_VALUE(ret);
    return(rc);
}


RETURNCODE_T	ciLocalUrsp(const utlAtParameterOp_T op,
	  const char					  *command_name_p,
	  const utlAtParameterValue_P2c   parameter_values_p,
	  const size_t					  num_parameters,
	  const char					  *info_text_p,
	  unsigned int					  *xid_p,
	  void							  *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T	  rc = INITIAL_RETURN_CODE;
	CiReturnCode	  ret = CIRC_FAIL;
	UINT32		  atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	int mode = 0;
	int len = 0;
	BOOL cmdValid = FALSE;
	char ursp_str[URSP_ENCODE_LEN_MAX];
	char ursp_bin[URSP_ENCODE_LEN_MAX];
	INT16 ursp_bin_len = 0;

	CHAR osId[MAX_LEN_OF_OS_ID + 1] = {0};
	INT16 osIdLen = 0;	
	CHAR appId[MAX_LEN_OF_OS_APP_ID + 1] = {0};
	INT16 appIdLen = 0;
	CHAR ipv4Addr[CI_PS_PDP_IP_V4_SIZE + 1] = {0};
	INT16 ipv4AddrLen = 0;
	CHAR ipv6Addr[CI_PS_PDP_IP_V6_SIZE + 1] = {0};
	INT16 ipv6AddrLen = 0;
	int protocolID = 0;
	int singleRemotePort  = 0;
	CHAR portRange[TEL_AT_APPSTART_7_PORT_RANGE_STR_MAX_LEN + 1]  = {0};
	INT16 portRangeLen = 0;
	CHAR DNN[TEL_AT_APPSTART_8_DNN_STR_MAX_LEN + 1]  = {0};
	INT16 DNNLen = 0;
	int scc_mode = 0;
	CHAR snssai[TEL_AT_CGDCONT_17_SNSSAI_STR_MAX_LEN + 1]  = {0};
	INT16 snssaiLen = 0;

	LocalURSPRule local_ursp = { 0 };

	*xid_p = atHandle;

	  switch( op )
	  {

	  case TEL_EXT_GET_CMD:/* AT*LOCALURSP? */
	  {
#if 0
  		len = read_ursp_local_file(ursp_bin, sizeof(ursp_bin));
  		if (len > 0)
  		{
	  		convertCiStr2AtStr(ursp_bin, len, ursp_str);
	  		snprintf(ursp_bin, sizeof(ursp_bin) - 1, "*LOCALURSP:%s", ursp_str);
	  		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, ursp_bin);
  		}
  		else
#endif
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		break;
	  }
  
	  case TEL_EXT_SET_CMD:/* AT*LOCALURSP= */
	  {	
		  if ( getExtValue( parameter_values_p, 0, &mode, 0, 2, 0 ) == TRUE )
		  {
		  	  if ( parameter_values_p[1].is_default == TRUE && (mode == 0 || mode == 2))
		  	  {
		  	  	cmdValid = TRUE;
		  	  	if (mode == 2)
		  	  	{
					unlink_ursp_local_file();
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
					break;
		  	  	}
		  	  	else
		  	  	{
		  	  		len = read_ursp_local_file(ursp_bin, sizeof(ursp_bin));
		  	  		if (len > 0)
		  	  		{
			  	  		convertCiStr2AtStr(ursp_bin, len, ursp_str);
			  	  		snprintf(ursp_bin, sizeof(ursp_bin) - 1, "*LOCALURSP:%s", ursp_str);
			  	  		ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, ursp_bin);
		  	  		}
		  	  		else
						ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
		  	  		
		  	  		break;
		  	  	}
		  	  }
		  	  else
		  	  {
		  	  #if 0
				  if ( getExtString( parameter_values_p, 1, ursp_str, sizeof(ursp_str), &len , (CHAR *)NULL) == TRUE )
				  {
					if (mode == 1 )
					{
						cmdValid = TRUE;
						convertHexToBin(ursp_str, (INT16)len, ursp_bin, &ursp_bin_len);
						if (write_ursp_local_file(ursp_bin, ursp_bin_len) > 0)
							ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
						else
							 ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);

						break;
					}
				  }
			   #endif
				if( getExtString( parameter_values_p, 1, (CHAR *)osId, MAX_LEN_OF_OS_ID, &osIdLen, NULL ) == TRUE )
				{
					if((parameter_values_p[1].is_default == TRUE) || !osIdLen)
						local_ursp.osIdPresent = FALSE;
					else
					{
						local_ursp.osIdPresent = TRUE;
						memcpy(local_ursp.osId, osId, osIdLen);
					}
				}
				else
					goto end;

				if ( getExtString( parameter_values_p, 2, (CHAR *)appId, MAX_LEN_OF_OS_APP_ID, &appIdLen, NULL ) == TRUE )
				{
					if((parameter_values_p[2].is_default == TRUE) || !appIdLen)
						local_ursp.appIdPresent = FALSE;
					else
					{
						local_ursp.appIdPresent = TRUE;
						memcpy(local_ursp.appId, appId, appIdLen);
					}
				}
				else 
					goto end;
				
				if ( getExtString( parameter_values_p, 3, ipv4Addr, TEL_AT_APPSTART_3_IPV4_ADDRESS_STR_MAX_LEN, &ipv4AddrLen, TEL_AT_APPSTART_3_IPV4_ADDRESS_STR_DEFAULT) == TRUE )
				{
					if((parameter_values_p[3].is_default == TRUE) || !ipv4AddrLen)
						local_ursp.ipv4AddrPresent = FALSE;
					else
					{
						local_ursp.ipv4AddrPresent = TRUE;
						memcpy(local_ursp.ipv4Addr, ipv4Addr, ipv4AddrLen);
					}
				}
				else
					goto end;
				
				if ( getExtString( parameter_values_p, 4, ipv6Addr, TEL_AT_APPSTART_4_IPV6_ADDRESS_STR_MAX_LEN, &ipv6AddrLen, TEL_AT_APPSTART_4_IPV6_ADDRESS_STR_DEFAULT) == TRUE )
				{
					if((parameter_values_p[4].is_default == TRUE) || !ipv6AddrLen)
						local_ursp.ipv6AddrPresent = FALSE;
					else
					{
						local_ursp.ipv6AddrPresent = TRUE;
						memcpy(local_ursp.ipv6Addr, ipv6Addr, ipv6AddrLen);
					}
				}
				else 
					goto end;
				
				if ( getExtValue( parameter_values_p, 5, &protocolID, TEL_AT_APPSTART_5_PROTOCOLID_VAL_MIN, TEL_AT_APPSTART_5_PROTOCOLID_VAL_MAX, TEL_AT_APPSTART_5_PROTOCOLID_VAL_DEFAULT ) == TRUE )
				{
					if(parameter_values_p[5].is_default == TRUE)
						local_ursp.protocolIDPresent = FALSE;
					else
					{
						local_ursp.protocolIDPresent = TRUE;
						local_ursp.protocolID = protocolID;
					}
				}
				else
					goto end;
				
									
				if( getExtValue( parameter_values_p, 6, &singleRemotePort, TEL_AT_APPSTART_6_SINGLE_PORT_VAL_MIN, TEL_AT_APPSTART_6_SINGLE_PORT_VAL_MAX , TEL_AT_APPSTART_6_SINGLE_PORT_VAL_DEFAULT)  == TRUE )
				{
					if(parameter_values_p[6].is_default == TRUE)
						local_ursp.singleRemotePortPresent = FALSE;
					else
					{
						local_ursp.singleRemotePortPresent = TRUE;
						local_ursp.singleRemotePort = singleRemotePort;
					}
				}
				else 
					goto end;
				
				if( getExtString( parameter_values_p, 7, portRange, TEL_AT_APPSTART_7_PORT_RANGE_STR_MAX_LEN, &portRangeLen , (CHAR *)TEL_AT_APPSTART_7_PORT_RANGE_STR_DEFAULT) == TRUE )
				{
					if((parameter_values_p[7].is_default == TRUE) || !portRangeLen)
						local_ursp.portRangePresent = FALSE;
					else
					{
						local_ursp.portRangePresent = TRUE;
						memcpy(local_ursp.portRange, portRange, portRangeLen);
					}
				}
				else
					goto end;
				
				if ( getExtString(parameter_values_p, 8, DNN, TEL_AT_APPSTART_8_DNN_STR_MAX_LEN, &DNNLen, TEL_AT_APPSTART_8_DNN_STR_DEFAULT) == TRUE )
				{
					if((parameter_values_p[8].is_default == TRUE) || !DNNLen)
						local_ursp.DNNPresent = FALSE;
					else
					{
						local_ursp.DNNPresent = TRUE;
						memcpy(local_ursp.DNN, DNN, DNNLen);
					}
				}
				else
					goto end;

				if(getExtValue(parameter_values_p, 9, &scc_mode, TEL_AT_CGDCONT_16_SSCMODE_VAL_MIN, TEL_AT_CGDCONT_16_SSCMODE_VAL_MAX, TEL_AT_CGDCONT_16_SSCMODE_VAL_DEFAULT) == TRUE)
				{
					if (parameter_values_p[9].is_default == TRUE)
						local_ursp.sccModePresent = FALSE;
					
					else
						local_ursp.sccModePresent = TRUE;
						local_ursp.sccMode = scc_mode;
				}
				else
					goto end;

				if ( getExtString( parameter_values_p, 10, snssai, TEL_AT_CGDCONT_17_SNSSAI_STR_MAX_LEN, &snssaiLen, TEL_AT_CGDCONT_17_SNSSAI_STR_DEFAULT) == TRUE )
				{
					if((parameter_values_p[10].is_default == TRUE) || !snssaiLen)
						local_ursp.snssaiPresent = FALSE;
					else
					{
						local_ursp.snssaiPresent = TRUE;
						memcpy( local_ursp.snssai, snssai, snssaiLen);
					} 
					cmdValid = TRUE;

					write_ursp_local_file(&local_ursp, sizeof(local_ursp));
				}
				else
					goto end;
		  	  }
		  }

end:  
		  if(cmdValid)
			 ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
		  else
			  ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_INVALID_PARAM,NULL);
		  
		  break;
	  }
  
	  default:
	  {
		  ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		  break;
	  }
	  }
  
	  /* handle the return value */
	  rc = HANDLE_RETURN_VALUE(ret);
	  return(rc);
}


#ifdef CTCC_SLICING_FEATURE

/************************************************************************************
* F@: ciZ5GTD - GLOBAL API for AT+Z5GTD -command
*
*/
RETURNCODE_T	  ciZ5GTD(				  const utlAtParameterOp_T op,
				  const char					  *command_name_p,
				  const utlAtParameterValue_P2c parameter_values_p,
				  const size_t num_parameters,
				  const char					  *info_text_p,
				  unsigned int					  *xid_p,
				  void							  *arg_p)
{
  RETURNCODE_T		rc = INITIAL_RETURN_CODE;
  CiReturnCode		ret = CIRC_FAIL;
  BOOL paraValid = FALSE;
  UINT8 defaultUrspFlag = 0;
  AppInfo rcvAppInfo;


  UNUSEDPARAM(op)
  UNUSEDPARAM(command_name_p)
  UNUSEDPARAM(parameter_values_p)
  UNUSEDPARAM(num_parameters)
  UNUSEDPARAM(info_text_p)
  /*
   *  Put parser index into the variable
   */
  CiRequestHandle atHandle;
  TelAtParserID sAtpIndex;

  sAtpIndex = *(TelAtParserID *)arg_p;
  atHandle = MAKE_AT_HANDLE( sAtpIndex );
  *xid_p = atHandle;


  /*
   * process operation
   */
  switch(op)
  {
	  case TEL_EXT_SET_CMD: 	 /* AT+Z5GTD= */
		  {
			  memset(&rcvAppInfo, 0, sizeof(rcvAppInfo));
			  
			  paraValid = setUrspPara(parameter_values_p, num_parameters, &rcvAppInfo, &defaultUrspFlag);

			  if(paraValid)
			  {
				  ret = ursp_app_start(0, &rcvAppInfo);
				  ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			  }
			  else
				  ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);

			  break;
		  }

	  case TEL_EXT_TEST_CMD:	/* AT+Z5GTD=? */		  
	  case TEL_EXT_GET_CMD: 	 /* AT+Z5GTD? */
		  {
			  ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			  break;
		  }

	  default:
		  ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
		  break;
  }

  /* handle the return value */
  rc = HANDLE_RETURN_VALUE(ret);
  return(rc);
}  
						
#endif	


/************************************************************************************
* F@: ciZ5GTD - GLOBAL API for AT+C5GURSPQRY -command
*
*/
RETURNCODE_T	  ciC5GURSPQRY( 			  const utlAtParameterOp_T op,
				  const char					  *command_name_p,
				  const utlAtParameterValue_P2c parameter_values_p,
				  const size_t num_parameters,
				  const char					  *info_text_p,
				  unsigned int					  *xid_p,
				  void							  *arg_p)
{
	RETURNCODE_T		rc = INITIAL_RETURN_CODE;
	CiReturnCode		ret = CIRC_FAIL;

	BOOL paraValid = FALSE;
	UINT8 defaultUrspFlag = 0;
	AppInfo rcvAppInfo;


	UNUSEDPARAM(op)
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(parameter_values_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)
	/*
	*  Put parser index into the variable
	*/
	CiRequestHandle atHandle;
	TelAtParserID sAtpIndex;

	sAtpIndex = *(TelAtParserID *)arg_p;
	atHandle = MAKE_AT_HANDLE( sAtpIndex );
	*xid_p = atHandle;


	/*
	* process operation
	*/
	switch(op)
	{
		case TEL_EXT_SET_CMD: 	 /* AT+C5GURSPQRY= */
		{
			memset(&rcvAppInfo, 0, sizeof(rcvAppInfo));

			paraValid = setUrspPara(parameter_values_p, num_parameters, &rcvAppInfo, &defaultUrspFlag);

			if(paraValid)
			{
				ret = getMatchedUrsp(atHandle, &rcvAppInfo,  &defaultUrspFlag);
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			}
			else
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);  

			break;
		}
  
		case TEL_EXT_GET_CMD: 	 /* AT+C5GURSPQRY? */
		{
			ret = getAllUrsp(atHandle, &rcvAppInfo,  &defaultUrspFlag);		
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
			break;
		}

		default:
			ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CME_OPERATION_NOT_SUPPORTED,NULL);
			break;
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}   


#endif

