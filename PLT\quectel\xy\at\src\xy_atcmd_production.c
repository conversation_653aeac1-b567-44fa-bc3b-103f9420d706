#include "xy_atcmd_production.h"
#include "xy_atcmd_async.h"
#include "xy_charger.h"
#include "xy_esim.h"
#include "xy_led.h"
#include "xy_key.h"
#include "ql_sim.h"
#include "ql_dev.h"
#include "ql_gpio.h"
#include "telatci.h"
#include "telcontroller.h"
#include "teldef.h"
#include "telutl.h"
#include "wan_applet.h"
#include "psm_module_def.h"
#include "duster_applets.h"
#include "sms_applet.h"

#include "ql_wifi.h"
#include "UART.h"
#include "uapapi.h"

#define BIT_STR(b, i) ((b) & (1 << (i)) ? '1' : '0')
#define BYTE_TO_BIN_STR(b)                                      \
    BIT_STR(b, 7), BIT_STR(b, 6), BIT_STR(b, 5), BIT_STR(b, 4), \
        BIT_STR(b, 3), BIT_STR(b, 2), BIT_STR(b, 1), BIT_STR(b, 0)

extern int set_wifi_auth_password_with_wpa_mode_common(char *password);
extern int user_paswd_update(void);
extern int psm_commit__(void);

/* SIM 卡切换 AT+XYSIM=<sim> */
RETURNCODE_T xy_sim_cmd(const utlAtParameterOp_T op,
                        const char *command_name_p,
                        const utlAtParameterValue_P2c parameter_values_p,
                        const size_t num_parameters,
                        const char *info_text_p,
                        unsigned int *xid_p,
                        void *arg_p)
{
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;
    xy_sim_switch_data_t switch_data;

    switch (op)
    {
    case TEL_EXT_GET_CMD: /* AT+XYSIM? */
    {
        break;
    }

    case TEL_EXT_SET_CMD: /* AT+XYSIM=<sim> */
    {
        int target_sim;

        if (getExtValue(parameter_values_p, 0, &target_sim, 0, 2, 0) != TRUE)
        {
            return HANDLE_RETURN_VALUE(ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL));
        }

        switch_data.target_sim = target_sim;
        break;
    }

    default:
    {
        return HANDLE_RETURN_VALUE(ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL));
    }
    }

    switch_data.op = op;
    if (xy_async_send_event(XY_ASYNC_EVENT_SIM_SWITCH, atHandle, &switch_data, sizeof(switch_data)) != 0)
    {
        return HANDLE_RETURN_VALUE(ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL));
    }

    return utlSUCCESS;
}

/* LED 灯测试 AT+XYLEDTEST=<on> */
RETURNCODE_T xy_led_test_cmd(const utlAtParameterOp_T op,
                             const char *command_name_p,
                             const utlAtParameterValue_P2c parameter_values_p,
                             const size_t num_parameters,
                             const char *info_text_p,
                             unsigned int *xid_p,
                             void *arg_p)
{
    RETURNCODE_T rc = INITIAL_RETURN_CODE;
    CiReturnCode ret = CIRC_FAIL;
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;

    switch (op)
    {
    case TEL_EXT_SET_CMD: /* AT+XYLEDTEST=<on> */
    {
        int led_test_enable;

        if (getExtValue(parameter_values_p, 0, &led_test_enable, 0, 1, 0) != TRUE)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (xy_led_set_test_mode(led_test_enable) != 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
        break;
    }

    default:
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
        break;
    }
    }

    rc = HANDLE_RETURN_VALUE(ret);
    return rc;
}

/* 恢复出厂设置 AT+XYFCTRESET */
RETURNCODE_T xy_fct_reset_cmd(const utlAtParameterOp_T op,
                              const char *command_name_p,
                              const utlAtParameterValue_P2c parameter_values_p,
                              const size_t num_parameters,
                              const char *info_text_p,
                              unsigned int *xid_p,
                              void *arg_p)
{
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;

    if (xy_async_send_event(XY_ASYNC_EVENT_FACTORY_RESET, atHandle, NULL, 0) != 0)
    {
        return HANDLE_RETURN_VALUE(ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL));
    }

    return utlSUCCESS;
}

/* 按键测试 AT+XYKEYTEST=<timeout> */
RETURNCODE_T xy_key_test_cmd(const utlAtParameterOp_T op,
                             const char *command_name_p,
                             const utlAtParameterValue_P2c parameter_values_p,
                             const size_t num_parameters,
                             const char *info_text_p,
                             unsigned int *xid_p,
                             void *arg_p)
{
    RETURNCODE_T rc = INITIAL_RETURN_CODE;
    CiReturnCode ret = CIRC_FAIL;
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;

    switch (op)
    {
    case TEL_EXT_SET_CMD: /* AT+XYKEYTEST=<timeout> */
    {
        int timeout_ms;

        if (getExtValue(parameter_values_p, 0, &timeout_ms, 1, 60000, 0) != TRUE)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (xy_key_start_test(timeout_ms, IND_REQ_HANDLE) != 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
        break;
    }

    default:
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
        break;
    }
    }

    rc = HANDLE_RETURN_VALUE(ret);
    return rc;
}

/* WiFi 供电开关 AT+XYWIFIPWR=<on> */
RETURNCODE_T xy_wifi_pwr_cmd(const utlAtParameterOp_T op,
                             const char *command_name_p,
                             const utlAtParameterValue_P2c parameter_values_p,
                             const size_t num_parameters,
                             const char *info_text_p,
                             unsigned int *xid_p,
                             void *arg_p)
{
    RETURNCODE_T rc = INITIAL_RETURN_CODE;
    CiReturnCode ret = CIRC_FAIL;
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
    ql_errcode_gpio_e ret_err;
    ql_gpio_level_e level;

    *xid_p = atHandle;

    ret_err = ql_gpio_get_level(GPIO_10, &level);
    if (ret_err != QL_GPIO_SUCCESS)
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED,
                     NULL);
        return HANDLE_RETURN_VALUE(ret);
    }

    switch (op)
    {
    case TEL_EXT_GET_CMD: /* AT+XYWIFIPWR? */
    {
        char response_buf[256];
        snprintf(response_buf, sizeof(response_buf), "+XYWIFIPWR: %d", level);
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, response_buf);
        break;
    }

    case TEL_EXT_SET_CMD: /* AT+XYWIFIPWR=<on> */
    {
        int on;
        int wait_init_wifi = 0;
        uap_config_param WlanCfg;

        if (getExtValue(parameter_values_p, 0, &on, 0, 1, 0) != TRUE)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM,
                         NULL);
            break;
        }

        if (on == level)
        {
            RTI_LOG("WiFi power state unchanged");
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
            break;
        }

        if (on == 1)
        {
            RTI_LOG("xy_wifi_pwr_cmd: Powering on WiFi");
            ql_gpio_set_level(GPIO_10, GPIO_HIGH);
            OSATaskSleep(1000);
            wifi_driver_init();
            int timeout = 100;
            while (timeout > 0)
            {
                if (GetWiFiInitStat() == 1)
                {
                    break;
                }
                OSATaskSleep(100);
                timeout--;
            }

            if (timeout == 0)
            {
                RTI_LOG("xy_wifi_pwr_cmd: WiFi initialization timeout");
                ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
                break;
            }

            // Configure WiFi after initialization
            ret = wlan_duster_config(DUSTER_CONFIG_START);
            if (ret != 0)
            {
                RTI_LOG("xy_wifi_pwr_cmd: WiFi configuration failed");
                ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
                break;
            }

            // Wait for configuration to complete
            timeout = 50; // 5 seconds timeout
            while (timeout > 0)
            {
                if (GetWifiConfigFlag() == 1)
                {
                    break;
                }
                OSATaskSleep(100);
                timeout--;
            }

            RTI_LOG("xy_wifi_pwr_cmd: WiFi powered on successfully");
        }
        else
        {
            UAP_BSS_Config(0);
            ql_gpio_set_level(GPIO_10, GPIO_LOW);
        }

        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
        break;
    }

    default:
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR,
                     CME_OPERATION_NOT_SUPPORTED, NULL);
        break;
    }
    }

    rc = HANDLE_RETURN_VALUE(ret);
    return rc;
}

/* WiFi 参数配置 AT+XYWIFICONF=<ssid_2.4G>,<ssid_5g>,<pwd> */
RETURNCODE_T xy_wifi_conf_cmd(const utlAtParameterOp_T op,
                              const char *command_name_p,
                              const utlAtParameterValue_P2c parameter_values_p,
                              const size_t num_parameters,
                              const char *info_text_p,
                              unsigned int *xid_p,
                              void *arg_p)
{
    RETURNCODE_T rc = INITIAL_RETURN_CODE;
    CiReturnCode ret = CIRC_FAIL;
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;

    switch (op)
    {
    case TEL_EXT_GET_CMD: /* AT+XYWIFICONF? */
    {
        char response_buf[256];
        char *wifi_ssid = get_wifi_ssid_ui();
        char auth_password[64] = {0};

        if (get_wifi_auth_password(auth_password, sizeof(auth_password)) != 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        snprintf(response_buf, sizeof(response_buf),
                 "+XYWIFICONF:\"%s\",\"\",\"%s\"",
                 wifi_ssid ? wifi_ssid : "",
                 auth_password);

        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, response_buf);
        break;
    }

    case TEL_EXT_SET_CMD:
    {
        CHAR ssid_24g[64] = {0};
        CHAR ssid_5g[64] = {0};
        CHAR password[64] = {0};
        INT16 ssid_24g_len, ssid_5g_len, password_len;

        if (!getExtString(parameter_values_p, 0, ssid_24g, sizeof(ssid_24g) - 1, &ssid_24g_len, NULL))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (!getExtString(parameter_values_p, 1, ssid_5g, sizeof(ssid_5g) - 1, &ssid_5g_len, NULL))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (!getExtString(parameter_values_p, 2, password, sizeof(password) - 1, &password_len, NULL))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (ssid_24g_len == 0 || password_len == 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        char *ucs2_ssid = utf8_to_unicode_char(ssid_24g);
        if (ucs2_ssid)
        {
            psm_set_wrapper(PSM_MOD_WLAN_SECURITY, NULL, "ssid", ucs2_ssid);
            sms_free(ucs2_ssid);
        }
        else
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        set_wifi_auth_password_with_wpa_mode_common(password);
        psm_commit__();

        if (xy_async_send_event(XY_ASYNC_EVENT_WIFI_CONFIG, atHandle, NULL, 0) != 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        ret = utlSUCCESS;
        break;
    }

    default:
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
        break;
    }
    }

    rc = HANDLE_RETURN_VALUE(ret);
    return rc;
}

static int parse_first_user_from_list(const char *user_list, char **username, char **password)
{
    if (!user_list || strlen(user_list) == 0)
        return -1;

    char *item_end = strchr(user_list, '^');
    if (!item_end)
        return -1;

    char *fd1 = strchr(user_list, '%');
    if (!fd1 || fd1 >= item_end)
        return -1;

    char *fd2 = strchr(fd1 + 1, '%');
    if (!fd2 || fd2 >= item_end)
        return -1;

    int username_len = fd1 - user_list;
    *username = duster_malloc(username_len + 1);
    if (*username)
    {
        memcpy(*username, user_list, username_len);
        (*username)[username_len] = '\0';
    }

    int password_len = fd2 - fd1 - 1;
    *password = duster_malloc(password_len + 1);
    if (*password)
    {
        memcpy(*password, fd1 + 1, password_len);
        (*password)[password_len] = '\0';
    }

    return (*username && *password) ? 0 : -1;
}

static int validate_ip_format(const char *ip)
{
    if (!ip || strlen(ip) == 0)
        return 0;

    int ip1, ip2, ip3, ip4;
    if (sscanf(ip, "%d.%d.%d.%d", &ip1, &ip2, &ip3, &ip4) != 4)
        return 0;

    if (ip1 < 0 || ip1 > 255 || ip2 < 0 || ip2 > 255 ||
        ip3 < 0 || ip3 > 255 || ip4 < 0 || ip4 > 255)
        return 0;

    return 1;
}

/* 验证网关IP是否在允许的变化范围内（只允许192.168网段） */
static int validate_gateway_ip_range(const char *new_ip)
{
    if (!validate_ip_format(new_ip))
        return 0;

    int ip1, ip2, ip3, ip4;
    sscanf(new_ip, "%d.%d.%d.%d", &ip1, &ip2, &ip3, &ip4);

    if (ip1 == 192 && ip2 == 168)
    {
        return 1;
    }

    return 0;
}

/* 自动调整DHCP范围以匹配新的网关IP */
static void update_dhcp_range_for_new_ip(const char *new_ip)
{
    char *dhcp_start = psm_get_wrapper(PSM_MOD_LAN, "dhcp", "start");
    char *dhcp_end = psm_get_wrapper(PSM_MOD_LAN, "dhcp", "end");

    if (!dhcp_start || !dhcp_end)
    {
        if (dhcp_start)
            duster_free(dhcp_start);
        if (dhcp_end)
            duster_free(dhcp_end);
        return;
    }

    int new_ip1, new_ip2, new_ip3, new_ip4;
    int start1, start2, start3, start4;
    int end1, end2, end3, end4;

    sscanf(new_ip, "%d.%d.%d.%d", &new_ip1, &new_ip2, &new_ip3, &new_ip4);
    sscanf(dhcp_start, "%d.%d.%d.%d", &start1, &start2, &start3, &start4);
    sscanf(dhcp_end, "%d.%d.%d.%d", &end1, &end2, &end3, &end4);

    char new_dhcp_start[32], new_dhcp_end[32];
    snprintf(new_dhcp_start, sizeof(new_dhcp_start), "%d.%d.%d.%d",
             new_ip1, new_ip2, new_ip3, start4);
    snprintf(new_dhcp_end, sizeof(new_dhcp_end), "%d.%d.%d.%d",
             new_ip1, new_ip2, new_ip3, end4);

    psm_set_wrapper(PSM_MOD_LAN, "dhcp", "start", new_dhcp_start);
    psm_set_wrapper(PSM_MOD_LAN, "dhcp", "end", new_dhcp_end);

    duster_free(dhcp_start);
    duster_free(dhcp_end);
}

/* WEBUI 参数配置 AT+XYWEBUICONF=<ip>,<user>,<pwd> */
RETURNCODE_T xy_webui_conf_cmd(const utlAtParameterOp_T op,
                               const char *command_name_p,
                               const utlAtParameterValue_P2c parameter_values_p,
                               const size_t num_parameters,
                               const char *info_text_p,
                               unsigned int *xid_p,
                               void *arg_p)
{
    RETURNCODE_T rc = INITIAL_RETURN_CODE;
    CiReturnCode ret = CIRC_FAIL;
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;

    switch (op)
    {
    case TEL_EXT_GET_CMD: /* AT+XYWEBUICONF? */
    {
        char response_buf[256];
        char *current_username = NULL;
        char *current_password = NULL;
        char *current_ip = NULL;

        current_ip = psm_get_wrapper(PSM_MOD_LAN, NULL, "ip");
        char *user_list = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "router_user_list");

        if (user_list && parse_first_user_from_list(user_list, &current_username, &current_password) == 0)
        {
            snprintf(response_buf, sizeof(response_buf),
                     "+XYWEBUICONF:\"%s\",\"%s\",\"%s\"",
                     current_ip ? current_ip : "***********",
                     current_username ? current_username : "admin",
                     current_password ? current_password : "admin");
        }
        else
        {
            char *get_username = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "router_username");
            char *get_password = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "router_password");

            snprintf(response_buf, sizeof(response_buf),
                     "+XYWEBUICONF:\"%s\",\"%s\",\"%s\"",
                     current_ip ? current_ip : "***********",
                     get_username ? get_username : "admin",
                     get_password ? get_password : "admin");

            if (get_username)
                duster_free(get_username);
            if (get_password)
                duster_free(get_password);
        }

        if (user_list)
            duster_free(user_list);
        if (current_username)
            duster_free(current_username);
        if (current_password)
            duster_free(current_password);
        if (current_ip)
            duster_free(current_ip);

        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, response_buf);
        break;
    }

    case TEL_EXT_SET_CMD: /* AT+XYWEBUICONF=<ip>,<user>,<pwd> */
    {
        CHAR ip[64] = {0};
        CHAR username[64] = {0};
        CHAR password[64] = {0};
        INT16 ip_len, username_len, password_len;

        if (!getExtString(parameter_values_p, 0, ip, sizeof(ip) - 1, &ip_len, NULL))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (!getExtString(parameter_values_p, 1, username, sizeof(username) - 1, &username_len, NULL))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (!getExtString(parameter_values_p, 2, password, sizeof(password) - 1, &password_len, NULL))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (ip_len == 0 || username_len == 0 || password_len == 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (!validate_gateway_ip_range(ip))
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        char new_user_list[256];
        snprintf(new_user_list, sizeof(new_user_list), "%s%%%s%%1^", username, password);

        psm_set_wrapper(PSM_MOD_MANAGEMENT, NULL, "router_user_list", new_user_list);
        psm_set_wrapper(PSM_MOD_MANAGEMENT, NULL, "router_username", username);
        psm_set_wrapper(PSM_MOD_MANAGEMENT, NULL, "router_password", password);
        user_paswd_update();

        psm_set_wrapper(PSM_MOD_LAN, NULL, "ip", ip);
        update_dhcp_range_for_new_ip(ip);
        psm_commit__();

        if (xy_async_send_event(XY_ASYNC_EVENT_WEBUI_CONFIG, atHandle, NULL, 0) != 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        ret = utlSUCCESS;
        break;
    }

    default:
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
        break;
    }
    }

    rc = HANDLE_RETURN_VALUE(ret);
    return rc;
}

/* 充电 IC 测试 AT+XYCHGREG=<addr>[,<reg>] */
RETURNCODE_T xy_chg_reg_cmd(const utlAtParameterOp_T op,
                            const char *command_name_p,
                            const utlAtParameterValue_P2c parameter_values_p,
                            const size_t num_parameters,
                            const char *info_text_p,
                            unsigned int *xid_p,
                            void *arg_p)
{
    RETURNCODE_T rc = INITIAL_RETURN_CODE;
    CiReturnCode ret = CIRC_FAIL;
    UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

    *xid_p = atHandle;

    switch (op)
    {
    case TEL_EXT_SET_CMD: /* AT+XYCHGREG=<addr>[,<reg>] */
    {
        char response_buf[64];
        int reg_addr;
        int reg_data;

        if (getExtValue(parameter_values_p, 0, &reg_addr, 0, 14, 0) != TRUE)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        if (parameter_values_p[1].is_default)
        {
            ret = hal_charger_read_reg((uint8_t)reg_addr, (uint8_t *)&reg_data);
            if (ret != 0)
            {
                ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
                break;
            }

            snprintf(response_buf, sizeof(response_buf),
                     "+XYCHGREG:%c%c%c%c%c%c%c%c", BYTE_TO_BIN_STR(reg_data));
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, response_buf);
            break;
        }

        if (getExtValue(parameter_values_p, 1, &reg_data, 0, 0xFF, 0) != TRUE)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_INVALID_PARAM, NULL);
            break;
        }

        ret = hal_charger_write_reg((uint8_t)reg_addr, (uint8_t)reg_data);
        if (ret != 0)
        {
            ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
            break;
        }

        ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, NULL);
        break;
    }

    default:
    {
        ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
        break;
    }
    }

    rc = HANDLE_RETURN_VALUE(ret);
    return rc;
}
