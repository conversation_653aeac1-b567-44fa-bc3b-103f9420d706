#define XY_LOG_USE_RTI 1
#include "ql_sim.h"
#include "ql_dev.h"
#include "xy_log.h"
#include "xy_atcmd_async.h"
#include "xy_esim.h"
#include "duster_applets.h"

#define AT_ASYNC_TASK_STACK_SIZE (2048)
#define AT_ASYNC_TASK_PRIORITY (68)
#define AT_ASYNC_MSGQ_SIZE (16)
#define AT_ASYNC_TASK_NAME "at_async_task"
#define AT_ASYNC_MSGQ_NAME "at_async_msgq"

extern int wlan_security_post_set(int Action, dc_args_t *dca);
extern int lan_post_set(int Action, dc_args_t *dca);

static OSATaskRef s_at_async_task_ref = NULL;
static OSMsgQRef s_at_async_msgq = NULL;
static bool s_at_async_initialized = false;
static uint8_t s_at_async_task_stack[AT_ASYNC_TASK_STACK_SIZE];

static const char at_async_tag[] = "AT_ASYNC";

static void at_async_handle_sim_switch(xy_async_msg_t *msg)
{
    xy_sim_switch_data_t *sim_data = (xy_sim_switch_data_t *)msg->data;
    switch (sim_data->op)
    {
    case TEL_EXT_GET_CMD: /* AT+XYSIM? */
    {
        char response_buf[64];
        xy_card_type_t current_card_type = xy_esim_get_current_card_type();
        if (current_card_type == XY_CARD_TYPE_UNKNOWN)
        {
            utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_ERROR);
            return;
        }
        snprintf(response_buf, sizeof(response_buf), "+XYSIM:%d", current_card_type);
        utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_INFO_TEXT, response_buf);
        break;
    }

    case TEL_EXT_SET_CMD: /* AT+XYSIM=<sim> */
    {
        if (xy_esim_switch_to_card_type((xy_card_type_t)sim_data->target_sim) != 0)
        {
            utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_ERROR);
            return;
        }
        utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_REQUEST_COMPLETED);
        break;
    }
    }
}

static void at_async_handle_factory_reset(xy_async_msg_t *msg)
{
    XY_LOGI(at_async_tag, "Processing factory reset");

    if (ql_dev_restore_default() != 0)
    {
        XY_LOGE(at_async_tag, "Factory reset failed");
        utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_ERROR);
    }
    else
    {
        XY_LOGI(at_async_tag, "Factory reset completed");
        utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_REQUEST_COMPLETED);
    }
}

static void at_async_handle_wifi_config(xy_async_msg_t *msg)
{
    XY_LOGI(at_async_tag, "Processing wifi config");

    if (wlan_security_post_set(DUSTER_CONFIG_SET, NULL) != 0)
    {
        XY_LOGE(at_async_tag, "Failed to set wifi config");
        utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_ERROR);
    }
    else
    {
        XY_LOGI(at_async_tag, "Wifi config completed");
        utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_REQUEST_COMPLETED);
    }
}

static void at_async_handle_webui_config(xy_async_msg_t *msg)
{
    XY_LOGI(at_async_tag, "Processing webui config");
    utlAtCommandResponse(msg->at_handle, utlAT_COMMAND_RESPONSE_TYPE_REQUEST_COMPLETED);
    lan_post_set(DUSTER_CONFIG_SET, NULL);
}

int xy_async_send_event(xy_async_event_type_t event_type, UINT32 at_handle, void *data, UINT32 data_len)
{
    if (!s_at_async_initialized)
    {
        XY_LOGE(at_async_tag, "AT Async module not init");
        return -1;
    }

    if (data_len > XY_ASYNC_MAX_DATA_SIZE)
    {
        XY_LOGE(at_async_tag, "Data size too large: %d, max: %d", data_len, XY_ASYNC_MAX_DATA_SIZE);
        return -1;
    }

    xy_async_msg_t msg = {
        .event_type = event_type,
        .at_handle = at_handle,
        .data_len = data_len,
    };

    if (data && data_len > 0)
    {
        memcpy(msg.data, data, data_len);
    }

    if (OSAMsgQSend(s_at_async_msgq, sizeof(xy_async_msg_t), (UINT8 *)&msg,
                    OS_NO_SUSPEND) != OS_SUCCESS)
    {
        XY_LOGE(at_async_tag, "Failed to send async event");
        return -1;
    }

    return 0;
}

static void at_async_event_task_entry(void *argv)
{
    XY_LOGI(at_async_tag, "AT Async AT Task started");

    xy_async_msg_t msg;
    while (1)
    {
        if (OSAMsgQRecv(s_at_async_msgq, (UINT8 *)&msg, sizeof(xy_async_msg_t),
                        OS_SUSPEND) != OS_SUCCESS)
        {
            XY_LOGE(at_async_tag, "AT Async Task: Failed to receive msg");
            OSATaskSleep(100);
            continue;
        }

        switch (msg.event_type)
        {
        case XY_ASYNC_EVENT_SIM_SWITCH:
            at_async_handle_sim_switch(&msg);
            break;

        case XY_ASYNC_EVENT_FACTORY_RESET:
            at_async_handle_factory_reset(&msg);
            break;

        case XY_ASYNC_EVENT_WIFI_CONFIG:
            at_async_handle_wifi_config(&msg);
            break;

        case XY_ASYNC_EVENT_WEBUI_CONFIG:
            at_async_handle_webui_config(&msg);
            break;

        default:
            XY_LOGE(at_async_tag, "AT Async Task: Unknown event type: %d", msg.event_type);
            utlAtCommandResponse(msg.at_handle, utlAT_COMMAND_RESPONSE_TYPE_ERROR);
            break;
        }
    }
}

int xy_async_init(void)
{
    if (s_at_async_initialized)
    {
        return 0;
    }

    XY_LOGI(at_async_tag, "Init AT Async module");

    if (OSAMsgQCreate(&s_at_async_msgq, AT_ASYNC_MSGQ_NAME, sizeof(xy_async_msg_t),
                      AT_ASYNC_MSGQ_SIZE, OS_FIFO) != OS_SUCCESS)
    {
        XY_LOGE(at_async_tag, "Failed to create AT Async message queue");
        return -1;
    }

    if (OSATaskCreate(&s_at_async_task_ref, s_at_async_task_stack, sizeof(s_at_async_task_stack),
                      AT_ASYNC_TASK_PRIORITY, AT_ASYNC_TASK_NAME, at_async_event_task_entry,
                      NULL) != OS_SUCCESS)
    {
        XY_LOGE(at_async_tag, "Failed to create AT Async task");
        OSAMsgQDelete(s_at_async_msgq);
        s_at_async_msgq = NULL;
        return -1;
    }

    s_at_async_initialized = true;
    XY_LOGI(at_async_tag, "AT Async module init successfully");
    return 0;
}
