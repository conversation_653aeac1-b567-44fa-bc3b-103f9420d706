#ifndef XY_ATCMD_ASYNC_H
#define XY_ATCMD_ASYNC_H

#include "teldef.h"
#include "utlAtParser.h"

#ifdef __cplusplus
extern "C" {
#endif

#define XY_ASYNC_MAX_DATA_SIZE (128)

typedef enum
{
    XY_ASYNC_EVENT_NONE = 0,
    XY_ASYNC_EVENT_SIM_SWITCH,    // SIM卡切换
    XY_ASYNC_EVENT_FACTORY_RESET, // 恢复出厂设置
    XY_ASYNC_EVENT_WIFI_CONFIG,   // 配置WIFI
    XY_ASYNC_EVENT_WEBUI_CONFIG,  // 配置WEBUI
    XY_ASYNC_EVENT_MAX
} xy_async_event_type_t;

typedef struct
{
    int target_sim;        // 目标SIM卡 (0: eSIM1, 1: eSIM2, 2: 物理SIM)
    utlAtParameterOp_T op; // 命令类型 (预留，可用于区分不同的SIM切换场景)
} xy_sim_switch_data_t;

typedef struct
{
    xy_async_event_type_t event_type;   // 事件类型
    UINT32 at_handle;                   // AT句柄
    UINT32 data_len;                    // 数据长度
    UINT8 data[XY_ASYNC_MAX_DATA_SIZE]; // 数据缓冲区
} xy_async_msg_t;

/**
 * @brief 初始化异步处理模块
 * @return 0: 成功, -1: 失败
 */
int xy_async_init(void);

/**
 * @brief 发送异步事件
 * @param event_type 事件类型
 * @param at_handle AT句柄
 * @param data 数据指针
 * @param data_len 数据长度
 * @return 0: 成功, -1: 失败
 */
int xy_async_send_event(xy_async_event_type_t event_type, UINT32 at_handle, void *data, UINT32 data_len);

#ifdef __cplusplus
}
#endif

#endif /* XY_ATCMD_ASYNC_H */